/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "blocks": {
    "load_video": "Videó betöltése: {{ description }}",
    "sold_out": "Elfogyott",
    "email_signup": {
      "label": "E-mail-cím",
      "placeholder": "E-mail-cím",
      "success": "Köszönjük feliratkozást!"
    },
    "filter": "Szűrés",
    "payment_methods": "<PERSON>zetési módok",
    "contact_form": {
      "name": "Név",
      "email": "E-mail-cím",
      "phone": "Telefonszám",
      "comment": "Hozzászólás",
      "post_success": "Köszönjük, hogy írtál nekünk. A lehető legrövidebb időn belül válaszolni fogunk.",
      "error_heading": "Kérjük, helyesbítsd a következőket:"
    },
    "unit_price": {
      "per_item_html": {
        "one": "{{price}}/{{count}} termék",
        "other": "{{price}}/{{count}} termék"
      },
      "per_unit_html": {
        "one": "{{price}}/{{count}} {{unit}}",
        "other": "{{price}}/{{count}} {{unit}}"
      },
      "per_unit_accessibility": {
        "single": "{{price}}/{{unit}}",
        "one": "{{price}}/{{count}} {{unit}}",
        "other": "{{price}}/{{count}} {{unit}}"
      },
      "unit": {
        "mg": "mg",
        "g": "g",
        "kg": "kg",
        "ml": "ml",
        "cl": "cl",
        "l": "l",
        "m3": "m³",
        "mm": "mm",
        "cm": "cm",
        "m": "m",
        "m2": "m²",
        "oz": "oz",
        "lb": "lb",
        "floz": "fl oz",
        "pt": "pt",
        "qt": "qt",
        "gal": "gal",
        "in": "in",
        "ft": "ft",
        "yd": "yd",
        "ft2": "ft²",
        "item": "termék"
      },
      "per_item_single_html": "{{price}} termékenként",
      "per_unit_single_html": "{{price}}/{{unit}}"
    }
  },
  "accessibility": {
    "play_model": "3D-modell lejátszása",
    "play_video": "Videó lejátszása",
    "unit_price": "Egységár",
    "country_results_count": "{{ count }} találat",
    "slideshow_pause": "Diavetítés megállítása",
    "slideshow_play": "Diavetítés indítása",
    "remove_item": "{{ title}} eltávolítása",
    "skip_to_text": "Ugrás a tartalomhoz",
    "skip_to_product_info": "Kihagyás, és ugrás a termékadatokra",
    "skip_to_results_list": "Ugrás a találati listára",
    "new_window": "Új ablakban nyílik meg.",
    "slideshow_next": "Következő dia",
    "slideshow_previous": "Előző dia",
    "close_dialog": "Párbeszédablak bezárása",
    "reset_search": "Keresés alaphelyzetbe állítása",
    "search_results_count": "{{ count }} találat a(z) „{{ query }}” kifejezésre",
    "search_results_no_results": "Nincs találat a(z) „{{ query }}” kifejezésre",
    "filters": "Szűrők",
    "filter_count": {
      "one": "{{ count }} szűrő alkalmazva",
      "other": "{{ count }} szűrő alkalmazva"
    },
    "account": "Fiók legördülő menü megnyitása",
    "cart": "Kosár",
    "cart_count": "Összes termék a kosárban",
    "menu": "Menü",
    "country_region": "Ország/régió",
    "slide_status": "{{ index }}./{{ length }} dia",
    "scroll_to": "Görgess ide: {{ title }}",
    "loading_product_recommendations": "Termékajánlások betöltése",
    "discount": "Kedvezménykód beváltása",
    "discount_applied": "Beváltott kedvezménykód: {{ code }}",
    "open_cart_drawer": "Kosárpanel megnyitása",
    "inventory_status": "Készlet állapota",
    "pause_video": "Videó szüneteltetése",
    "find_country": "Ország keresése",
    "localization_region_and_language": "Régió- és nyelvválasztó megnyitása",
    "open_search_modal": "Modális keresőablak megnyitása",
    "decrease_quantity": "Mennyiség csökkentése",
    "increase_quantity": "Mennyiség növelése"
  },
  "actions": {
    "add_to_cart": "Hozzáadás a kosárhoz",
    "clear_all": "Az összes törléses",
    "remove": "Eltávolítás",
    "view_in_your_space": "Megtekintés a saját környezetben",
    "show_filters": "Szűrés",
    "clear": "Törlés",
    "continue_shopping": "Vásárlás folytatása",
    "log_in_html": "Már van fiókod? <a href=\"{{ link }}\">Jelentkezz be</a> a gyorsabb fizetéshez.",
    "see_items": {
      "one": "{{ count }} termék megtekintése",
      "other": "{{ count }} termék megtekintése"
    },
    "view_all": "Az összes megtekintése",
    "add": "Hozzáadás",
    "choose": "Kiválasztás",
    "added": "Hozzáadva",
    "show_less": "Kevesebb megjelenítése",
    "show_more": "Több megjelenítése",
    "close": "Bezárás",
    "more": "Egyebek",
    "zoom": "Nagyítás",
    "close_dialog": "Párbeszédablak bezárása",
    "reset": "Alaphelyzetbe állítás",
    "enter_using_password": "Belépés jelszóval",
    "submit": "Beküldés",
    "enter_password": "Jelszó megadása",
    "back": "Vissza",
    "log_in": "Bejelentkezés",
    "log_out": "Kijelentkezés",
    "remove_discount": "Kedvezménykód ({{ code }}) eltávolítása",
    "view_store_information": "Webáruház adatai",
    "apply": "Beváltás",
    "sign_in_options": "További bejelentkezési lehetőségek",
    "sign_up": "Regisztráció",
    "open_image_in_full_screen": "Kép megnyitása teljes képernyőn",
    "sort": "Rendezés",
    "show_all_options": "Összes lehetőség megjelenítése"
  },
  "content": {
    "reviews": "összegzés",
    "no_results_found": "Nincs találat",
    "language": "Nyelv",
    "localization_region_and_language": "Régió és nyelv",
    "cart_total": "Kosár végösszege",
    "your_cart_is_empty": "A kosarad üres",
    "product_image": "Termékkép",
    "product_information": "Termékadatok",
    "quantity": "Mennyiség",
    "product_total": "Termék végösszege",
    "cart_estimated_total": "Becsült végösszeg",
    "seller_note": "Különleges utasítások",
    "cart_subtotal": "Részösszeg",
    "discounts": "Kedvezmények",
    "discount": "Kedvezmény",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat és az adókat. A kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy": "Tartalmazza a vámokat és az adókat. A kedvezmények és a szállítási költség kiszámítása a pénztárban történik.",
    "taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza az adókat. A kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.",
    "taxes_included_shipping_at_checkout_without_policy": "Tartalmazza az adókat. A kedvezmények és a szállítási költség kiszámítása a pénztárban történik.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat. Az adók, a kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Tartalmazza a vámokat. Az adók, a kedvezmények és a szállítási költség kiszámítása a pénztárban történik.",
    "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Az adók, a kedvezmények és a <a href=\"{{ link }}\">szállítási költség</a> kiszámítása a pénztárban történik..",
    "taxes_at_checkout_shipping_at_checkout_without_policy": "Az adók, a kedvezmények és a szállítási költség kiszámítása a pénztárban történik.",
    "cart_title": "Kosár",
    "price": "Ár",
    "price_regular": "Normál ár",
    "price_compare_at": "Összehasonlítás ár alapján",
    "price_sale": "Akciós ár",
    "checkout": "Fizetés",
    "duties_and_taxes_included": "Tartalmazza a vámokat és az adókat.",
    "duties_included": "Tartalmazza a vámokat.",
    "shipping_policy_html": "A fizetéskor kiszámított <a href=\"{{ link }}\">szállítási költség</a>.",
    "taxes_included": "Tartalmazza az adókat.",
    "product_badge_sold_out": "Elfogyott",
    "product_badge_sale": "Akciós",
    "grid_view": {
      "default_view": "Alapértelmezett",
      "grid_fieldset": "Oszloprács",
      "single_item": "Önálló",
      "zoom_out": "Felnagyítás"
    },
    "search_input_label": "Keresés",
    "search_input_placeholder": "Keresés",
    "search_results": "Találatok",
    "search_results_label": "Találatok",
    "search_results_no_results": "Nincs találat erre: „{{ terms }}”. Próbálj meg másra rákeresni.",
    "search_results_resource_articles": "Blogbejegyzések",
    "search_results_resource_collections": "Kollekciók",
    "search_results_resource_pages": "Oldal",
    "search_results_resource_products": "Termékek",
    "search_results_resource_queries": "Javaslatok keresése",
    "search_results_view_all": "Összes megtekintése",
    "search_results_view_all_button": "Összes megtekintése",
    "search_results_resource_products_count": {
      "one": "{{ count }} termék",
      "other": "{{ count }} termék"
    },
    "recently_viewed_products": "Nemrégiben megtekintett",
    "unavailable": "Nem áll rendelkezésre",
    "collection_placeholder": "Kollekció címe",
    "product_card_placeholder": "Termék címe",
    "product_count": "Termékek száma",
    "item_count": {
      "one": "{{ count }} termék",
      "other": "{{ count }} termék"
    },
    "errors": "Hibák",
    "price_from": "Indulóár: {{ price }}",
    "featured_products": "Kiemelt termékek",
    "no_products_found": "Nincs találat.",
    "use_fewer_filters_html": "Próbálj meg kevesebb szűrőt használni, vagy <a class=\"{{ class }}\" href=\"{{ link }}\">töröld az összes szűrőt</a>.",
    "search": "Keresés",
    "search_results_no_results_check_spelling": "Nincs találat erre: „{{ terms }}”. Ellenőrizd a helyesírást, vagy írj be egy másik szót vagy kifejezést.",
    "filters": "Szűrők",
    "price_filter_html": "A legmagasabb ár {{ price }}",
    "blog_details_separator": "|",
    "read_more": "Bővebben…",
    "wrong_password": "Hibás jelszó",
    "account_title": "Fiók",
    "account_title_personalized": "Kedves {{ first_name }}!",
    "account_orders": "Rendelések",
    "account_profile": "Profil",
    "discount_code": "Kedvezménykód",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza a vámokat és az adókat. A szállítási díjat a pénztárnál számítjuk ki.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza a vámokat és az adókat. A szállítási díjat a pénztárnál számítjuk ki.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza a vámokat. A szállítási díjat a pénztárnál számítjuk ki.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza a vámokat. A szállítási díjat a pénztárnál számítjuk ki.",
    "pickup_available_at_html": "Személyesen átvehető itt: <b>{{ location }}</b>",
    "pickup_available_in": "Személyesen átvehető ekkor: {{ pickup_time }}",
    "pickup_not_available": "Személyes átvétel jelenleg nem érhető el",
    "pickup_ready_in": "{{ pickup_time }}",
    "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Az adókat és a <a href=\"{{ link }}\">szállítási díjat</a> a pénztárnál számítjuk ki.",
    "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Az adókat és a szállítási díjat a pénztárnál számítjuk ki.",
    "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Tartalmazza az adókat. A szállítási díjat a pénztárnál számítjuk ki.",
    "taxes_included_shipping_at_checkout_without_policy_without_discounts": "Tartalmazza az adókat. A szállítási díjat a pénztárnál számítjuk ki.",
    "view_more_details": "További részletek megtekintése",
    "inventory_low_stock": "Alacsony készlet",
    "inventory_in_stock": "Készleten",
    "inventory_out_of_stock": "Nincs készleten",
    "page_placeholder_title": "Oldal címe",
    "page_placeholder_content": "Jelölj ki egy oldalt a tartalma megjelenítéséhez.",
    "placeholder_image": "Helyőrző kép",
    "inventory_low_stock_show_count": {
      "one": "Elérhető összeg: {{ count }}",
      "other": "Elérhető összeg: {{ count }}"
    },
    "shipping_policy": "A fizetéskor kiszámított szállítási költség.",
    "discount_code_error": "A kedvezménykód nem érvényesíthető a kosaradon",
    "shipping_discount_error": "A szállítási kedvezmények a fizetéskor jelennek meg a cím megadását követően",
    "powered_by": "A bolt szolgáltatója:",
    "store_owner_link_html": "Te vagy az áruház tulajdonosa? <a href=\"{{ link }}\">Jelentkezz be itt</a>"
  },
  "gift_cards": {
    "issued": {
      "how_to_use_gift_card": "Az ajándékkártya kódja online, a QR-kód pedig az üzletben használható fel",
      "title": "Íme a(z) {{ shop }} üzletben levásárolható, {{ value }} értékű ajándékkártyád!",
      "subtext": "Ajándékkártya",
      "shop_link": "Webáruház megnyitása",
      "add_to_apple_wallet": "Hozzáadás az Apple Wallethoz",
      "qr_image_alt": "Ezt a QR-kódot beszkennelve beválthatod az ajándékkártyát.",
      "copy_code": "Ajándékkártya kódjának másolása",
      "expiration_date": "Lejárat dátuma: {{ expires_on }}",
      "copy_code_success": "Sikeres volt a kód másolása",
      "expired": "Lejárt"
    }
  },
  "placeholders": {
    "password": "Jelszó",
    "search": "Keresés",
    "product_title": "Termék megnevezése",
    "collection_title": "Kollekció megnevezése"
  },
  "products": {
    "product": {
      "add_to_cart": "Hozzáadás a kosárhoz",
      "added_to_cart": "Hozzáadva a kosárhoz",
      "adding_to_cart": "Hozzáadás…",
      "add_to_cart_error": "Nem tudtuk hozzáadni a terméket a kosárhoz",
      "sold_out": "Elfogyott",
      "unavailable": "Nincs készleten"
    }
  },
  "fields": {
    "separator": "–"
  },
  "blogs": {
    "article": {
      "comment_author_separator": "•",
      "comments_heading": {
        "one": "{{ count }} hozzászólás",
        "other": "{{ count }} hozzászólás"
      }
    },
    "comment_form": {
      "email": "E‑mail-cím",
      "error": "A hozzászólás közzététele nem sikerült, kérjük, ügyelj a következőkre:",
      "heading": "Hozzászólás írása",
      "message": "Üzenet",
      "moderated": "Felhívjuk a figyelmedet, hogy közzététel előtt a hozzászólásokat jóvá kell hagyni.",
      "name": "Név",
      "post": "Hozzászólás elküldése",
      "success_moderated": "Hozzászólás közzétéve, moderálásra vár",
      "success": "Hozzászólás közzétéve"
    }
  },
  "pagefly": {
    "products": {
      "product": {
        "regular_price": "Regular price",
        "sold_out": "Sold out",
        "unavailable": "Unavailable",
        "on_sale": "Sale",
        "quantity": "Quantity",
        "add_to_cart": "Add to cart",
        "back_to_collection": "Back to {{ title }}",
        "view_details": "View details"
      }
    },
    "article": {
      "tags": "Tags:",
      "all_topics": "All topics",
      "by_author": "by {{ author }}",
      "posted_in": "Posted in",
      "read_more": "Read more",
      "back_to_blog": "Back to {{ title }}"
    },
    "comments": {
      "title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Message",
      "post": "Post comment",
      "moderated": "Please note, comments must be approved before they are published",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.",
      "success": "Your comment was posted successfully! Thank you!",
      "comments_with_count": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    },
    "password_page": {
      "login_form_message": "Enter store using password:",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_submit": "Enter",
      "signup_form_email_label": "Email",
      "signup_form_success": "We will send you an email right before we open!",
      "password_link": "Enter using password"
    }
  }
}
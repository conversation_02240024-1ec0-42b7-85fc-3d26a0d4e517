/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "blocks": {
    "load_video": "加载视频：{{ description }}",
    "sold_out": "售罄",
    "email_signup": {
      "label": "电子邮件",
      "placeholder": "电子邮件地址",
      "success": "感谢您的订阅！"
    },
    "filter": "筛选条件",
    "payment_methods": "付款方式",
    "contact_form": {
      "name": "名称",
      "email": "电子邮件",
      "phone": "电话",
      "comment": "评论",
      "post_success": "感谢您联系我们。我们会尽快回复您。",
      "error_heading": "请调整以下内容："
    },
    "unit_price": {
      "per_item_html": {
        "one": "{{count}} 件商品为 {{price}}",
        "other": "{{count}} 件商品为 {{price}}"
      },
      "per_unit_html": {
        "one": "{{price}}/{{count}}{{unit}}",
        "other": "{{price}}/{{count}}{{unit}}"
      },
      "per_unit_accessibility": {
        "single": "{{price}}/{{unit}}",
        "one": "{{price}}/{{count}}{{unit}}",
        "other": "{{price}}/{{count}}{{unit}}"
      },
      "unit": {
        "mg": "毫克",
        "g": "克",
        "kg": "千克",
        "ml": "毫升",
        "cl": "厘升",
        "l": "升",
        "m3": "立方米",
        "mm": "毫米",
        "cm": "厘米",
        "m": "米",
        "m2": "平方米",
        "oz": "盎司",
        "lb": "磅",
        "floz": "液量盎司",
        "pt": "品脱",
        "qt": "夸脱",
        "gal": "加仑",
        "in": "英寸",
        "ft": "英尺",
        "yd": "码",
        "ft2": "平均英尺",
        "item": "商品"
      },
      "per_item_single_html": "单价为 {{price}}",
      "per_unit_single_html": "{{price}}/{{unit}}"
    }
  },
  "accessibility": {
    "play_model": "演示 3D 模型",
    "play_video": "播放视频",
    "unit_price": "单价",
    "country_results_count": "{{ count }} 个结果",
    "slideshow_pause": "暂停幻灯片",
    "slideshow_play": "播放幻灯片",
    "remove_item": "删除 {{ title}}",
    "skip_to_text": "跳到内容",
    "skip_to_product_info": "跳至产品信息",
    "skip_to_results_list": "跳到结果列表",
    "new_window": "在新窗口中打开。",
    "slideshow_next": "下一张幻灯片",
    "slideshow_previous": "上一张幻灯片",
    "close_dialog": "关闭对话框",
    "reset_search": "重置搜索",
    "search_results_count": "找到“{{ query }}”的 {{ count }} 条搜索结果",
    "search_results_no_results": "未找到“{{ query }}”的相关结果",
    "filters": "筛选条件",
    "account": "打开账户下拉菜单",
    "cart": "购物车",
    "cart_count": "购物车中的商品总数",
    "filter_count": {
      "one": "已应用 {{ count }} 个筛选条件",
      "other": "已应用 {{ count }} 个筛选条件"
    },
    "menu": "菜单",
    "country_region": "国家/地区",
    "slide_status": "第 {{ index }} 张幻灯片，共 {{ length }} 张",
    "scroll_to": "滚动到 {{ title }}",
    "loading_product_recommendations": "加载产品推荐",
    "discount": "应用折扣码",
    "discount_applied": "已应用折扣码：{{ code }}",
    "open_cart_drawer": "打开购物车抽屉",
    "inventory_status": "库存状态",
    "pause_video": "暂停视频",
    "localization_region_and_language": "打开区域和语言选择器",
    "open_search_modal": "打开搜索模态窗口",
    "find_country": "查找国家/地区",
    "decrease_quantity": "减少数量",
    "increase_quantity": "增加数量"
  },
  "actions": {
    "add_to_cart": "添加到购物车",
    "clear_all": "全部清除",
    "remove": "删除",
    "view_in_your_space": "在您的空间中查看",
    "show_filters": "筛选条件",
    "clear": "清除",
    "continue_shopping": "继续购物",
    "log_in_html": "已有账户？<a href=\"{{ link }}\">登录</a>以快速结账。",
    "see_items": {
      "one": "查看 {{ count }} 件商品",
      "other": "查看 {{ count }} 件商品"
    },
    "view_all": "查看全部",
    "add": "添加",
    "choose": "选择",
    "added": "已添加",
    "show_less": "显示部分",
    "show_more": "显示更多",
    "close": "关闭",
    "more": "更多",
    "reset": "重置",
    "zoom": "缩放",
    "close_dialog": "关闭对话框",
    "back": "返回",
    "log_in": "登录",
    "log_out": "登出",
    "remove_discount": "删除折扣 {{ code }}",
    "enter_using_password": "使用密码进入",
    "submit": "提交",
    "enter_password": "输入密码",
    "view_store_information": "查看商店信息",
    "apply": "应用",
    "sign_in_options": "其他登录选项",
    "sign_up": "注册",
    "open_image_in_full_screen": "全屏打开图片",
    "sort": "排序",
    "show_all_options": "显示所有选项"
  },
  "content": {
    "reviews": "评价",
    "no_results_found": "未找到结果",
    "language": "语言",
    "localization_region_and_language": "区域和语言",
    "cart_total": "购物车总金额",
    "your_cart_is_empty": "您的购物车为空",
    "product_image": "产品图片",
    "product_information": "产品信息",
    "quantity": "数量",
    "product_total": "产品总计",
    "cart_estimated_total": "预计总额",
    "seller_note": "特殊说明",
    "cart_subtotal": "小计",
    "discounts": "折扣",
    "discount": "折扣",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "已含关税和税费。结账时计算折扣和<a href=\"{{ link }}\">运费</a>。",
    "duties_and_taxes_included_shipping_at_checkout_without_policy": "已含关税和税费。结账时计算折扣和运费。",
    "taxes_included_shipping_at_checkout_with_policy_html": "已含税费。结账时计算折扣和<a href=\"{{ link }}\">运费</a>。",
    "taxes_included_shipping_at_checkout_without_policy": "已含税费。结账时计算折扣和运费。",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "已含关税。结账时计算税费、折扣和<a href=\"{{ link }}\">运费</a>。",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "已含关税。结账时计算税费、折扣和运费。",
    "taxes_at_checkout_shipping_at_checkout_with_policy_html": "结账时计算税费、折扣和<a href=\"{{ link }}\">运费</a>。",
    "taxes_at_checkout_shipping_at_checkout_without_policy": "结账时计算税费、折扣和运费。",
    "checkout": "结账",
    "cart_title": "购物车",
    "price": "价格",
    "price_regular": "常规价格",
    "price_compare_at": "原价",
    "price_sale": "促销价",
    "duties_and_taxes_included": "已含关税和税费。",
    "duties_included": "已含关税。",
    "shipping_policy_html": "结账时计算的<a href=\"{{ link }}\">运费</a>。",
    "taxes_included": "已含税费。",
    "product_badge_sold_out": "售罄",
    "product_badge_sale": "促销",
    "grid_view": {
      "default_view": "默认",
      "grid_fieldset": "列网格",
      "single_item": "单个",
      "zoom_out": "缩小"
    },
    "search_input_label": "搜索",
    "search_input_placeholder": "搜索",
    "search_results": "搜索结果",
    "search_results_label": "搜索结果",
    "search_results_no_results": "未找到“{{ terms }}”的相关结果。尝试其他搜索词。",
    "search_results_resource_articles": "博客文章",
    "search_results_resource_collections": "产品系列",
    "search_results_resource_pages": "页面",
    "search_results_resource_products": "产品",
    "search_results_resource_queries": "搜索建议",
    "search_results_view_all": "查看全部",
    "search_results_view_all_button": "查看全部",
    "search_results_resource_products_count": {
      "one": "{{ count }} 件产品",
      "other": "{{ count }} 件产品"
    },
    "recently_viewed_products": "最近的浏览记录",
    "unavailable": "不可用",
    "collection_placeholder": "产品系列标题",
    "product_card_placeholder": "产品标题",
    "product_count": "产品数量",
    "item_count": {
      "one": "{{ count }} 件商品",
      "other": "{{ count }} 件商品"
    },
    "errors": "错误",
    "search": "搜索",
    "search_results_no_results_check_spelling": "未找到“{{ terms }}”的相关结果。请检查拼写或使用其他词或短语。",
    "featured_products": "特色产品",
    "no_products_found": "找不到产品。",
    "price_from": "{{ price }} 起",
    "use_fewer_filters_html": "尝试减少使用的筛选条件数量，或<a class=\"{{ class }}\" href=\"{{ link }}\">清除所有筛选条件</a>。",
    "filters": "筛选条件",
    "price_filter_html": "最高价格为 {{ price }}",
    "blog_details_separator": "|",
    "read_more": "阅读详细内容…",
    "account_title": "账户",
    "account_title_personalized": "您好，{{ first_name }}",
    "account_orders": "订单",
    "account_profile": "资料",
    "discount_code": "折扣码",
    "pickup_available_at_html": "<b>{{ location }}</b> 提供取货服务",
    "pickup_available_in": "提供取货服务：{{ pickup_time }}",
    "pickup_not_available": "目前无法提供取货服务",
    "pickup_ready_in": "{{ pickup_time }}",
    "wrong_password": "密码错误",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "已含关税和税费。结账时计算运费。",
    "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "已含关税和税费。结账时计算运费。",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "已含关税。结账时计算运费。",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "已含关税。结账时计算运费。",
    "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "结账时计算的税费和<a href=\"{{ link }}\">运费</a>。",
    "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "结账时计算的税费和运费。",
    "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "已含税费。结账时计算运费。",
    "taxes_included_shipping_at_checkout_without_policy_without_discounts": "已含税费。结账时计算运费。",
    "view_more_details": "查看更多详细信息",
    "inventory_low_stock": "低库存",
    "inventory_in_stock": "现货",
    "inventory_out_of_stock": "缺货",
    "page_placeholder_title": "页面标题",
    "page_placeholder_content": "选择一个页面来显示其内容。",
    "placeholder_image": "占位符图像",
    "inventory_low_stock_show_count": {
      "one": "剩余 {{ count }}",
      "other": "剩余 {{ count }}"
    },
    "shipping_discount_error": "添加地址后，结账时会显示运费折扣",
    "discount_code_error": "折扣码无法应用于您的购物车",
    "shipping_policy": "结账时计算运费。",
    "powered_by": "此商店依托",
    "store_owner_link_html": "您是否为店主？<a href=\"{{ link }}\">在此处登录</a>"
  },
  "gift_cards": {
    "issued": {
      "how_to_use_gift_card": "在线使用礼品卡代码或在店内使用二维码",
      "title": "您的 {{ shop }} 的礼品卡余额为 {{ value }}！",
      "subtext": "您的礼品卡",
      "shop_link": "访问在线商店",
      "add_to_apple_wallet": "添加到 Apple Wallet",
      "qr_image_alt": "二维码 — 扫描兑换礼品卡",
      "copy_code": "复制礼品卡代码",
      "expiration_date": "过期日期：{{ expires_on }}",
      "copy_code_success": "已成功复制代码",
      "expired": "已过期"
    }
  },
  "placeholders": {
    "password": "密码",
    "search": "搜索",
    "product_title": "产品标题",
    "collection_title": "产品系列标题"
  },
  "products": {
    "product": {
      "add_to_cart": "添加到购物车",
      "added_to_cart": "添加到购物车",
      "adding_to_cart": "正在添加...",
      "add_to_cart_error": "添加到购物车时出错",
      "sold_out": "售罄",
      "unavailable": "不可售"
    }
  },
  "fields": {
    "separator": "到"
  },
  "blogs": {
    "article": {
      "comment_author_separator": "•",
      "comments_heading": {
        "one": "{{ count }} 条评论",
        "other": "{{ count }} 条评论"
      }
    },
    "comment_form": {
      "email": "电子邮件",
      "error": "评论无法发布，请解决以下问题：",
      "heading": "发表评论",
      "message": "消息",
      "moderated": "请注意，评论必须在发布之前获得批准。",
      "name": "名称",
      "post": "发布评论",
      "success_moderated": "评论已发布，等待审核",
      "success": "评论已发布"
    }
  },
  "pagefly": {
    "products": {
      "product": {
        "regular_price": "Regular price",
        "sold_out": "Sold out",
        "unavailable": "Unavailable",
        "on_sale": "Sale",
        "quantity": "Quantity",
        "add_to_cart": "Add to cart",
        "back_to_collection": "Back to {{ title }}",
        "view_details": "View details"
      }
    },
    "article": {
      "tags": "Tags:",
      "all_topics": "All topics",
      "by_author": "by {{ author }}",
      "posted_in": "Posted in",
      "read_more": "Read more",
      "back_to_blog": "Back to {{ title }}"
    },
    "comments": {
      "title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Message",
      "post": "Post comment",
      "moderated": "Please note, comments must be approved before they are published",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.",
      "success": "Your comment was posted successfully! Thank you!",
      "comments_with_count": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    },
    "password_page": {
      "login_form_message": "Enter store using password:",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_submit": "Enter",
      "signup_form_email_label": "Email",
      "signup_form_success": "We will send you an email right before we open!",
      "password_link": "Enter using password"
    }
  }
}
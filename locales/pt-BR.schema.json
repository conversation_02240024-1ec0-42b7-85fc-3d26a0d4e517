/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "borders": "Bordas",
    "collapsible_row": "Linha recolhível",
    "colors": "Cores",
    "custom_section": "Seção personalizada",
    "icon": "Ícone",
    "logo_and_favicon": "Logo e favicon",
    "overlapping_blocks": "Blocos sobrepostos",
    "product_buy_buttons": "Botões Comprar",
    "product_description": "Descrição",
    "product_price": "Preço",
    "product_variant_picker": "Se<PERSON>or de variante",
    "slideshow": "Apresentação de slides",
    "typography": "Tipografia",
    "video": "Vídeo",
    "slideshow_controls": "Controles para apresentação de slides",
    "size": "Tamanho",
    "spacing": "Espaçamento",
    "product_recommendations": "Produtos recomendados",
    "product_media": "Mídias do produto",
    "featured_collection": "Coleção em destaque",
    "add_to_cart": "Adicionar ao carrinho",
    "email_signup": "Assinante de e-mail",
    "submit_button": "Botão Enviar",
    "grid_layout_selector": "Seletor de layout de grade",
    "image": "Imagem",
    "list_items": "Itens da lista",
    "facets": "Facetas",
    "variants": "Variantes",
    "product_cards": "Cartões de produtos",
    "styles": "Estilos",
    "buttons": "Botões",
    "inputs": "Entradas",
    "primary_button": "Botão principal",
    "secondary_button": "Botão secundário",
    "popovers": "Pop-overs",
    "marquee": "Faixa",
    "alternating_content_rows": "Linhas alternadas",
    "pull_quote": "Citação em destaque",
    "contact_form": "Formulário de contato",
    "featured_product": "Destaques do produto",
    "icons_with_text": "Ícones com texto",
    "accelerated_checkout": "checkout acelerado",
    "accordion": "Menus sanfona",
    "accordion_row": "Linha sanfona",
    "animations": "Animações",
    "announcement": "Comunicado",
    "announcement_bar": "Barra de avisos",
    "badges": "Selos",
    "button": "Botão",
    "cart": "Carrinho",
    "cart_items": "Itens do carrinho",
    "cart_products": "Produtos do carrinho",
    "cart_title": "Carrinho",
    "collection": "Coleção",
    "collection_card": "Cartão de coleção",
    "collection_columns": "Colunas de coleção",
    "collection_container": "Coleção",
    "collection_description": "Descrição de coleção",
    "collection_image": "Imagem da coleção",
    "collection_info": "Informações da coleção",
    "collection_list": "Lista de coleções",
    "collections": "Coleções",
    "content": "Conteúdo",
    "content_grid": "Grade de conteúdo",
    "details": "Informações",
    "divider": "Divisor",
    "filters": "Filtragem e organização",
    "follow_on_shop": "Seguir no Shop",
    "footer": "Rodapé",
    "footer_utilities": "Utilitários do rodapé",
    "group": "Grupo",
    "header": "Cabeçalho",
    "heading": "Título",
    "icons": "Ícones",
    "image_with_text": "Imagem com texto",
    "input": "Entrada",
    "logo": "Logo",
    "magazine_grid": "Grade de revistas",
    "media": "Mídias",
    "menu": "Menu",
    "mobile_layout": "Layout em dispositivos móveis",
    "payment_icons": "Ícones de pagamento",
    "popup_link": "Link de pop-up",
    "predictive_search": "Pesquisar pop-over",
    "predictive_search_empty": "Pesquisa preditiva vazia",
    "price": "Preço",
    "product": "Produto",
    "product_card": "Cartão de produto",
    "product_card_media": "Mídias",
    "product_card_rendering": "Renderização de cartão de produto",
    "product_grid": "Grade",
    "product_grid_main": "Grade de produtos",
    "product_image": "Imagem do produto",
    "product_information": "Informações do produto",
    "product_list": "Lista de produtos",
    "product_review_stars": "Estrelas de avaliação",
    "quantity": "Quantidade",
    "row": "Linha",
    "search": "Pesquisa",
    "section": "Seção",
    "selected_variants": "Variantes selecionadas",
    "shop_the_look": "Comprar o look",
    "slide": "Slide",
    "social_media_links": "Links de redes sociais",
    "spacer": "Separador",
    "steps": "Etapas",
    "summary": "Resumo",
    "swatches": "Amostras",
    "testimonials": "Depoimentos",
    "text": "Texto",
    "title": "Título",
    "utilities": "Serviços públicos",
    "search_input": "Termos de pesquisa",
    "search_results": "Resultados da pesquisa",
    "read_only": "Somente leitura",
    "404": "404",
    "collection_title": "Título da coleção",
    "collections_bento": "Lista de coleções: Bento",
    "faq_section": "Perguntas frequentes",
    "hero": "Imagem de destaque",
    "jumbo_text": "Texto do Jumbo",
    "view_all_button": "Ver tudo",
    "video_section": "Vídeo",
    "blog": "Blog",
    "blog_posts": "Posts do blog",
    "custom_liquid": "Liquid personalizado",
    "blog_post": "Post do blog",
    "caption": "Legenda",
    "collection_card_image": "Imagem",
    "collection_links": "Links de coleção",
    "collection_links_spotlight": "Links de coleção: Destaque",
    "collection_links_text": "Links de coleção: Texto",
    "collections_carousel": "Lista de coleções: Carrossel",
    "collections_editorial": "Lista de coleções: editorial",
    "collections_grid": "Lista de coleções: Grade",
    "copyright": "Direitos autorais",
    "count": "Contagem",
    "divider_section": "Divisor",
    "drawers": "Menus deslizantes",
    "editorial": "Editorial",
    "editorial_jumbo_text": "Editorial: texto do Jumbo",
    "hero_marquee": "Principal: marca de seleção",
    "input_fields": "Campos de entrada",
    "local_pickup": "Retirada no local",
    "marquee_section": "Marca de seleção",
    "media_with_text": "Mídia com texto",
    "page": "Página",
    "page_content": "Conteúdo",
    "page_layout": "Layout de página",
    "policy_list": "Links para políticas",
    "prices": "Preços",
    "products_carousel": "Lista de produtos: Carrossel",
    "products_editorial": "Lista de produtos: editorial",
    "products_grid": "Lista de produtos: Grade",
    "social_link": "Link de redes sociais",
    "split_showcase": "Dividir mostruário",
    "variant_pickers": "Seletores de variante",
    "pills": "Pílulas",
    "product_title": "Título do produto",
    "large_logo": "Logo grande",
    "product_list_button": "Botão \"Ver tudo\"",
    "product_inventory": "Estoque de produtos"
  },
  "settings": {
    "alignment": "Alinhamento",
    "autoplay": "Reprodução automática",
    "background": "Plano de fundo",
    "border_radius": "Raio dos cantos",
    "border_width": "Espessura da borda",
    "borders": "Bordas",
    "bottom_padding": "Preenchimento inferior",
    "button": "Botão",
    "color": "Cor",
    "colors": "Cores",
    "content_alignment": "Alinhamento de conteúdo",
    "content_direction": "Direção do conteúdo",
    "content_position": "Posição do conteúdo",
    "cover_image_size": "Tamanho da imagem de capa",
    "cover_image": "Imagem de capa",
    "custom_minimum_height": "Altura mínima personalizada",
    "custom_width": "Largura personalizada",
    "enable_video_looping": "Loop de vídeo",
    "favicon": "Favicon",
    "font_family": "Família de fontes",
    "gap": "Lacuna",
    "geometric_translate_y": "Translação geométrica Y",
    "heading": "Título",
    "icon": "Ícone",
    "image": "Image",
    "image_icon": "Ícone de imagem",
    "image_opacity": "Opacidade da imagem",
    "image_position": "Posição da imagem",
    "image_ratio": "Proporção da imagem",
    "label": "Etiqueta",
    "line_height": "Altura da linha",
    "link": "Link",
    "layout_gap": "Lacuna de layout",
    "make_section_full_width": "Definir seção com largura total",
    "minimum_height": "Altura mínima",
    "opacity": "Opacidade",
    "overlay_opacity": "Opacidade de sobreposição",
    "padding": "Preenchimento",
    "primary_color": "Links",
    "product": "Produto",
    "section_width": "Largura da seção",
    "size": "Tamanho",
    "slide_spacing": "Intervalo entre slides",
    "slide_width": "Largura do slide",
    "slideshow_fullwidth": "Slides com largura total",
    "style": "Estilo",
    "text": "Texto",
    "text_case": "Caixa",
    "top_padding": "Preenchimento superior",
    "video": "Vídeo",
    "video_alt_text": "Texto alternativo",
    "video_loop": "Repetir vídeo",
    "video_position": "Posição do vídeo",
    "width": "Largura",
    "z_index": "Índice Z",
    "limit_content_width": "Limitar a largura do conteúdo",
    "color_scheme": "Esquema de cores",
    "inherit_color_scheme": "Herdar esquema de cores",
    "product_count": "Contagem de produtos",
    "product_type": "Tipo de produto",
    "content_width": "Largura do conteúdo",
    "collection": "Coleção",
    "enable_sticky_content": "Conteúdo persistente no desktop",
    "error_color": "Erro",
    "success_color": "Sucesso",
    "primary_font": "Fonte principal",
    "secondary_font": "Fonte secundária",
    "tertiary_font": "Fonte terciária",
    "columns": "Colunas",
    "items_to_show": "Itens a mostrar",
    "layout": "Layout",
    "layout_type": "Tipo",
    "show_grid_layout_selector": "Mostrar o seletor de layout de grade",
    "view_more_show": "Mostrar o botão \"Ver mais\"",
    "image_gap": "Lacuna entre imagens",
    "width_desktop": "Largura em desktop",
    "width_mobile": "Largura em dispositivos móveis",
    "border_style": "Estilo da borda",
    "height": "Altura",
    "thickness": "Espessura",
    "stroke": "Traço",
    "filter_style": "Filtrar estilo",
    "swatches": "Amostras",
    "quick_add_colors": "Adição rápida de cores",
    "divider_color": "Divisor",
    "border_opacity": "Opacidade da borda",
    "hover_background": "Fundo ao passar o cursor",
    "hover_borders": "Bordas ao passar o cursor",
    "hover_text": "Texto ao passar o cursor",
    "primary_hover_color": "Links ao passar o cursor",
    "primary_button_text": "Texto do botão principal",
    "primary_button_background": "Fundo do botão principal",
    "primary_button_border": "Borda do botão principal",
    "secondary_button_text": "Texto do botão secundário",
    "secondary_button_background": "Fundo do botão secundário",
    "secondary_button_border": "Borda do botão secundário",
    "shadow_color": "Sombra",
    "limit_media_to_screen_height": "Ajustar à altura da tela",
    "video_autoplay": "Reprodução automática",
    "video_cover_image": "Imagem de capa",
    "video_external_url": "URL",
    "video_source": "Fonte",
    "first_row_media_position": "Posição da mídia na primeira linha",
    "accordion": "Menus sanfona",
    "aspect_ratio": "Proporção",
    "auto_rotate_announcements": "Girar automaticamente os comunicados",
    "auto_rotate_slides": "Girar automaticamente os slides",
    "background_color": "Cor de fundo",
    "badge_corner_radius": "Raio dos cantos",
    "badge_position": "Posição em cartões",
    "badge_sale_color_scheme": "Promoção",
    "badge_sold_out_color_scheme": "Esgotado",
    "behavior": "Comportamento",
    "blur": "Desfoque de sombra",
    "border": "Borda",
    "bottom": "Parte inferior",
    "card_image_height": "Altura da imagem do produto",
    "carousel_on_mobile": "Carrossel em dispositivos móveis",
    "cart_count": "Contagem de carrinhos",
    "cart_items": "Itens do carrinho",
    "cart_related_products": "Produtos relacionados",
    "cart_title": "Carrinho",
    "cart_total": "Total do carrinho",
    "cart_type": "Tipo",
    "case": "Caixa",
    "checkout_buttons": "Botões de checkout acelerado",
    "collection_list": "Coleções",
    "collection_templates": "Modelos de coleção",
    "content": "Conteúdo",
    "corner_radius": "Raio dos cantos",
    "country_region": "País/Região",
    "currency_code": "Código da moeda",
    "custom_height": "Altura personalizada",
    "custom_mobile_size": "Tamanho personalizado do dispositivo móvel",
    "desktop_height": "Altura para desktop",
    "direction": "Direção",
    "display": "Exibição",
    "divider_thickness": "Espessura do divisor",
    "divider": "Divisor",
    "dividers": "Divisores",
    "drop_shadow": "Sombra projetada",
    "empty_state_collection_info": "Exibido antes de inserir uma pesquisa",
    "empty_state_collection": "Coleção de estado vazio",
    "enable_filtering": "Filtros",
    "enable_grid_density": "Controle de layout de grade",
    "enable_sorting": "Organização",
    "enable_zoom": "Habilitar zoom",
    "equal_columns": "Colunas iguais",
    "expand_first_group": "Expandir primeiro grupo",
    "extend_media_to_screen_edge": "Estender mídia até a borda da tela",
    "extend_summary": "Estender até a borda da tela",
    "extra_large": "Extragrande",
    "extra_small": "Extrapequeno",
    "fixed_height": "Altura do dispositivo",
    "fixed_width": "Largura do dispositivo",
    "flag": "Bandeira",
    "font_price": "Fonte do preço",
    "font_weight": "Peso da fonte",
    "font": "Fonte",
    "full_width_first_image": "Primeira imagem de largura total",
    "full_width_on_mobile": "Largura total em dispositivos móveis",
    "heading_preset": "Predefinição de título",
    "hide_padding": "Ocultar preenchimento",
    "hide_unselected_variant_media": "Ocultar mídia de variante não selecionada",
    "horizontal_gap": "Lacuna horizontal",
    "horizontal_offset": "Compensação horizontal de sombra",
    "hover_behavior": "Comportamento ao passar o cursor",
    "icon_background": "Plano de fundo do ícone",
    "icons": "Ícones",
    "image_border_radius": "Raio de canto da imagem",
    "installments": "Parcelamento",
    "integrated_button": "Botão integrado",
    "language_selector": "Seletor de idioma",
    "large": "Grande",
    "left_padding": "Preenchimento à esquerda",
    "left": "Esquerda",
    "letter_spacing": "Espaçamento entre letras",
    "limit_product_details_width": "Limitar largura das informações de produto",
    "link_preset": "Predefinição de link",
    "links": "Links",
    "logo_font": "Fonte do logo",
    "logo": "Logo",
    "loop": "Loop",
    "make_details_sticky_desktop": "Persistente no desktop",
    "max_width": "Largura máxima",
    "media_height": "Altura da mídia",
    "media_overlay": "Sobreposição de mídia",
    "media_position": "Posição da mídia",
    "media_type": "Tipo de mídia",
    "media_width": "Largura da mídia",
    "menu": "Menu",
    "mobile_columns": "Colunas em dispositivos móveis",
    "mobile_height": "Altura em dispositivos móveis",
    "mobile_logo_image": "Logo em dispositivo móvel",
    "mobile_quick_add": "Adição rápida em dispositivos móveis",
    "motion_direction": "Direção",
    "motion": "Movimento",
    "movement_direction": "Direção de movimento",
    "navigation_bar_color_scheme": "Esquema de cores da barra de navegação",
    "navigation_bar": "Barra de navegação",
    "navigation": "Navegação",
    "open_new_tab": "Abrir link em uma nova aba",
    "overlay_color": "Cor de sobreposição",
    "overlay": "Sobreposição",
    "padding_bottom": "Preenchimento inferior",
    "padding_horizontal": "Preenchimento horizontal",
    "padding_top": "Preenchimento superior",
    "page_width": "Largura da página",
    "pagination": "Paginação",
    "percent_height": "Altura percentual",
    "percent_size_mobile": "Tamanho percentual",
    "percent_size": "Tamanho percentual",
    "percent_width": "Largura percentual",
    "pixel_size_mobile": "Tamanho do pixel",
    "pixel_size": "Tamanho do pixel",
    "placement": "Posicionamento",
    "position": "Posição",
    "preset": "Predefinição",
    "product_cards": "Cartões de produtos",
    "product_pages": "Páginas de produtos",
    "product_templates": "Modelos de produtos",
    "products": "Produtos",
    "quick_add": "Adição rápida",
    "ratio": "Proporção",
    "regular": "Regular",
    "review_count": "Revisar contagem",
    "right": "Direita",
    "row_height": "Altura da linha",
    "row": "Linha",
    "seller_note": "Permitir observação para o vendedor",
    "shape": "Formato",
    "show_as_accordion": "Exibir como sanfona em dispositivos móveis",
    "show_sale_price_first": "Exibir preço promocional primeiro",
    "show_tax_info": "Informação de tributos",
    "show": "Exibir",
    "size_mobile": "Tamanho do dispositivo móvel",
    "small": "Pequeno",
    "speed": "Velocidade",
    "statement": "Extrato",
    "sticky_header": "Cabeçalho fixo",
    "text_hierarchy": "Hierarquia de texto",
    "text_presets": "Predefinições de texto",
    "title": "Título",
    "top": "Parte superior",
    "type": "Tipo",
    "type_preset": "Predefinição de texto",
    "underline_thickness": "Espessura do sublinhado",
    "unit": "Unidade",
    "variant_images": "Imagens de variantes",
    "vendor": "Fabricante",
    "vertical_gap": "Lacuna vertical",
    "vertical_offset": "Compensação vertical de sombra",
    "vertical_on_mobile": "Vertical em dispositivo móveis",
    "view_all_as_last_card": "\"Ver tudo\" como último cartão",
    "weight": "Peso",
    "wrap": "Ajustar",
    "read_only": "Somente leitura",
    "always_stack_buttons": "Sempre empilhe botões",
    "custom_mobile_width": "Largura personalizada no dispositivo móvel",
    "gradient_direction": "Direção do gradiente",
    "headings": "Títulos",
    "overlay_style": "Estilo da sobreposição",
    "shadow_opacity": "Opacidade da sombra",
    "show_filter_label": "Etiquetas de texto para filtros aplicados",
    "show_swatch_label": "Etiquetas de texto para amostras",
    "transparent_background": "Fundo transparente",
    "hide_logo_on_home_page": "Ocultar logo na página inicial",
    "account": "Conta",
    "align_baseline": "Alinhar linha de base do texto",
    "add_discount_code": "Permitir descontos no carrinho",
    "background_overlay": "Sobreposição de plano de fundo",
    "background_media": "Mídia de fundo",
    "border_thickness": "Espessura da borda",
    "bottom_row": "Linha inferior",
    "button_text_case": "Estilo do texto",
    "button_text_weight": "Peso do texto",
    "card_size": "Tamanho do cartão",
    "auto_open_cart_drawer": "\"Adicionar ao carrinho\" automaticamente abre o carrinho deslizante",
    "collection_count": "Contagem de coleções",
    "collection_title_case": "Caixa do título de coleção",
    "custom_liquid": "Código Liquid",
    "default": "Padrão",
    "default_logo": "Logo padrão",
    "divider_width": "Largura do divisor",
    "horizontal_padding": "Preenchimento horizontal",
    "inverse": "Inverso",
    "inverse_logo": "Logo inverso",
    "layout_style": "Estilo",
    "length": "Comprimento",
    "mobile_card_size": "Tamanho do cartão em dispositivos móveis",
    "mobile_pagination": "Paginação móvel",
    "open_row_by_default": "Abrir linha como padrão",
    "page": "Página",
    "page_transition_enabled": "Transição da página",
    "product_and_card_title_case": "Caixa do título do produto e cartão",
    "product_title_case": "Caixa do título do produto",
    "right_padding": "Preenchimento à direita",
    "search": "Pesquisa",
    "search_icon": "Ícone de pesquisa",
    "search_position": "Posição",
    "search_row": "Linha",
    "show_author": "Autoria",
    "show_alignment": "Mostrar alinhamento",
    "show_count": "Mostrar contagem",
    "show_date": "Data",
    "show_pickup_availability": "Exibir disponibilidade de retirada",
    "show_search": "Exibir pesquisa",
    "text_label_case": "Caixa da etiqueta do texto",
    "use_inverse_logo": "Usar logo inverso",
    "vertical_padding": "Preenchimento vertical",
    "visibility": "Visibilidade",
    "product_corner_radius": "Raio do canto do produto",
    "card_corner_radius": "Raio do canto do cartão",
    "alignment_mobile": "Alinhamento em dispositivos móveis",
    "animation_repeat": "Repetir animação",
    "blurred_reflection": "Reflexão desfocada",
    "card_hover_effect": "Efeito ao passar o cartão",
    "effects": "Efeitos",
    "inventory_threshold": "Limite de estoque baixo",
    "reflection_opacity": "Opacidade da reflexão",
    "show_inventory_quantity": "Mostrar quantidade de estoque baixo",
    "transition_to_main_product": "Transição do cartão do produto para a página do produto"
  },
  "options": {
    "adapt_to_image": "Adaptar à imagem",
    "apple": "Maçã",
    "arrow": "Seta",
    "auto": "Automático",
    "banana": "Banana",
    "bottle": "Garrafa",
    "box": "Caixa",
    "buttons": "Botões",
    "carrot": "Cenoura",
    "center": "Centro",
    "chat_bubble": "Balão de chat",
    "clipboard": "Prancheta",
    "contain": "Contém",
    "counter": "Contador",
    "cover": "Capa",
    "custom": "Personalizado",
    "dairy_free": "Sem lactose",
    "dairy": "Laticínios",
    "default": "Padrão",
    "dropdowns": "Menus suspensos",
    "dots": "Pontos",
    "dryer": "Secador",
    "end": "Término",
    "eye": "Olho",
    "facebook": "Facebook",
    "fill": "Preenchimento",
    "fire": "Fogo",
    "fit": "Ajuste",
    "full": "Total",
    "full_and_page": "Plano de fundo em tela cheia, conteúdo limitado pela largura da página",
    "gluten_free": "Sem glúten",
    "heading": "Título",
    "heart": "Coração",
    "horizontal": "Horizontal",
    "instagram": "Instagram",
    "iron": "Ferro",
    "landscape": "Paisagem",
    "large": "Grande",
    "leaf": "Folha",
    "leather": "Couro",
    "lg": "G",
    "lightning_bolt": "Relâmpago",
    "link": "Link",
    "lipstick": "Batom",
    "lock": "Cadeado",
    "lowercase": "minúscula",
    "m": "M",
    "map_pin": "Marcador de mapa",
    "medium": "Médio",
    "none": "Nenhuma",
    "numbers": "Números",
    "nut_free": "Sem nozes",
    "outline": "Contorno",
    "page": "Página",
    "pants": "Calças",
    "paw_print": "Pegada de animal",
    "pepper": "Pimenta",
    "perfume": "Perfume",
    "pinterest": "Pinterest",
    "plane": "Avião",
    "plant": "Planta",
    "portrait": "Retrato",
    "price_tag": "Etiqueta de preço",
    "question_mark": "Ponto de interrogação",
    "recycle": "Reciclar",
    "return": "Retorno",
    "ruler": "Régua",
    "s": "P",
    "sentence": "Sentença",
    "serving_dish": "Travessa",
    "shirt": "Camisa",
    "shoe": "Sapato",
    "silhouette": "Silhueta",
    "small": "Pequena",
    "snapchat": "Snapchat",
    "snowflake": "Floco de neve",
    "solid": "Sólido",
    "space_between": "Espaço entre",
    "square": "Quadrada",
    "star": "Estrela",
    "start": "Início",
    "stopwatch": "Cronômetro",
    "tiktok": "TikTok",
    "truck": "Caminhão",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "uppercase": "Letras maiúsculas",
    "vertical": "Vertical",
    "vimeo": "Vimeo",
    "washing": "Lavagem",
    "circle": "Círculo",
    "swatches": "Amostras",
    "full_and_page_offset_left": "Plano de fundo em tela cheia, conteúdo limitado pela largura da página, compensação esquerda",
    "full_and_page_offset_right": "Plano de fundo em tela cheia, conteúdo limitado pela largura da página, compensação direita",
    "offset_left": "Compensação esquerda",
    "offset_right": "Compensação direita",
    "page_center_aligned": "Página, alinhamento no centro",
    "page_left_aligned": "Página, alinhamento à esquerda",
    "page_right_aligned": "Página, alinhamento à direita",
    "button": "Botão",
    "caption": "Legenda",
    "h1": "Título 1",
    "h2": "Título 2",
    "h3": "Título 3",
    "h4": "Título 4",
    "h5": "Título 5",
    "h6": "Título 6",
    "paragraph": "Parágrafo",
    "primary": "Principal",
    "secondary": "Secundária",
    "tertiary": "Terciária",
    "chevron_left": "Chevron para esquerda",
    "chevron_right": "Chevron para direita",
    "diamond": "Diamante",
    "grid": "Grade",
    "parallelogram": "Paralelogramo",
    "rounded": "Arredondado",
    "fit_content": "Ajuste",
    "pills": "Pílulas",
    "heavy": "Grosso",
    "thin": "Fino",
    "drawer": "Menu deslizante",
    "preview": "Pré-visualizar",
    "text": "Texto",
    "video_uploaded": "Upload concluído",
    "video_external_url": "URL externo",
    "aspect_ratio": "Proporção",
    "fixed": "Fixo",
    "pixel": "Pixel",
    "percent": "Percentual",
    "above_carousel": "Acima do carrossel",
    "all": "Tudo",
    "up": "Para cima",
    "down": "Para baixo",
    "always": "Sempre",
    "arrows_large": "Setas grandes",
    "arrows": "Setas",
    "balance": "Saldo",
    "bento": "Bento",
    "black": "Preto",
    "bluesky": "Bluesky",
    "body_large": "Corpo (grande)",
    "body_regular": "Corpo (regular)",
    "body_small": "Corpo (pequeno)",
    "bold": "Negrito",
    "bottom_left": "Canto inferior esquerdo",
    "bottom_right": "Canto inferior direito",
    "bottom": "Parte inferior",
    "capitalize": "Letra maiúscula",
    "caret": "Cursor",
    "carousel": "Carrossel",
    "check_box": "Caixa de seleção",
    "chevron_large": "Chevrons grandes",
    "chevron": "Chevron",
    "chevrons": "Chevrons",
    "classic": "Clássico",
    "collection_images": "Imagens da coleção",
    "color": "Cor",
    "complementary": "Complementar",
    "dissolve": "Dissolver",
    "dotted": "Pontilhado",
    "editorial": "Editorial",
    "extra_large": "Extragrande",
    "extra_small": "Extrapequeno",
    "featured_collections": "Coleções em destaque",
    "featured_products": "Produtos em destaque",
    "font_primary": "Principal",
    "font_secondary": "Secundária",
    "font_tertiary": "Terciária",
    "forward": "Avançar",
    "full_screen": "Tela cheia",
    "gradient": "Gradiente",
    "heading_extra_large": "Cabeçalho (extragrande)",
    "heading_extra_small": "Cabeçalho (extrapequeno)",
    "heading_large": "Cabeçalho (grande)",
    "heading_regular": "Cabeçalho (regular)",
    "heading_small": "Cabeçalho (pequeno)",
    "icon": "Ícone",
    "image": "Image",
    "input": "Entrada",
    "inside_carousel": "Dentro do carrossel",
    "inverse_large": "Inverso grande",
    "inverse": "Inverso",
    "large_arrows": "Setas grandes",
    "large_chevrons": "Chevrons grandes",
    "left": "Esquerda",
    "light": "Claro",
    "linkedin": "LinkedIn",
    "loose": "Solto",
    "media_first": "Mídia primeiro",
    "media_second": "Mídia em segundo lugar",
    "modal": "Modal",
    "narrow": "Estreita",
    "never": "Nunca",
    "next_to_carousel": "Ao lado do carrossel",
    "normal": "Normal",
    "nowrap": "Sem ajustar",
    "off_media": "Na mídia",
    "on_media": "Fora da mídia",
    "on_scroll_up": "Na rolagem da página para cima",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "Em formato de pílula",
    "plus": "Plus",
    "pretty": "Bonito(a)",
    "price": "Preço",
    "primary_style": "Estilo principal",
    "rectangle": "Retângulo",
    "regular": "Regular",
    "related": "Relacionado",
    "reverse": "Inverso",
    "rich_text": "Rich text",
    "right": "Direita",
    "secondary_style": "Estrilo secundário",
    "semibold": "Seminegrito",
    "shaded": "Sombreado",
    "show_second_image": "Mostrar segundo imagem",
    "single": "Único",
    "slide_left": "Deslizar para a esquerda",
    "slide_up": "Deslizar para cima",
    "spotify": "Spotify",
    "stack": "Empilhado",
    "text_only": "Apenas texto",
    "threads": "Threads",
    "thumbnails": "Miniaturas",
    "tight": "Fixo",
    "top_left": "Canto superior esquerdo",
    "top_right": "Canto superior direito",
    "top": "Parte superior",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Sublinhado",
    "video": "Vídeo",
    "wide": "Larga",
    "youtube": "YouTube",
    "accent": "Destaque",
    "below_image": "Abaixo da imagem",
    "body": "Corpo",
    "button_primary": "Botão principal",
    "button_secondary": "Botão secundário",
    "compact": "Compacto",
    "crop_to_fit": "Cortar para caber",
    "hidden": "Oculto",
    "hint": "Dica",
    "maintain_aspect_ratio": "Manter proporção",
    "off": "Desativado",
    "on_image": "Na imagem",
    "social_bluesky": "Rede social: Bluesky",
    "social_facebook": "Rede social: Facebook",
    "social_instagram": "Rede social: Instagram",
    "social_linkedin": "Rede social: LinkedIn",
    "social_pinterest": "Rede social: Pinterest",
    "social_snapchat": "Rede social: Snapchat",
    "social_spotify": "Rede social: Spotify",
    "social_threads": "Rede social: Threads",
    "social_tiktok": "Rede social: TikTok",
    "social_tumblr": "Rede social: Tumblr",
    "social_twitter": "Rede social: X (Twitter)",
    "social_whatsapp": "Rede social: WhatsApp",
    "social_vimeo": "Rede social: Vimeo",
    "social_youtube": "Rede social: YouTube",
    "spotlight": "Spotlight",
    "standard": "Padrão",
    "subheading": "Subtítulo",
    "blur": "Desfoque",
    "lift": "Elevação",
    "reveal": "Revelar",
    "scale": "Escala",
    "subtle_zoom": "Zoom"
  },
  "content": {
    "advanced": "Avançado",
    "background_image": "Imagem de fundo",
    "background_video": "Vídeo de fundo",
    "block_size": "Tamanho do bloco",
    "borders": "Bordas",
    "describe_the_video_for": "Descreva o vídeo para clientes que usam leitores de tela. [Saiba mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "section_size": "Tamanho da seção",
    "slideshow_width": "Largura do slide",
    "typography": "Tipografia",
    "width_is_automatically_optimized": "A largura é otimizada automaticamente para dispositivos móveis.",
    "complementary_products": "Os produtos complementares precisam ser configurados usando o app Search & Discovery. [Saiba mais](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "As colunas são otimizadas automaticamente para dispositivos móveis",
    "content_width": "A largura do conteúdo só se aplica quando a largura da seção estiver configurada para largura total.",
    "adjustments_affect_all_content": "Aplicável a todo o conteúdo neste bloco",
    "responsive_font_sizes": "Os tamanhos são dimensionados automaticamente para todos os tamanhos de tela",
    "buttons": "Botões",
    "swatches": "Amostras",
    "variant_settings": "Configurações de variante",
    "background": "Plano de fundo",
    "appearance": "Aparência",
    "arrows": "Setas",
    "body_size": "Tamanho do corpo",
    "mobile_size": "Tamanho do dispositivo móvel",
    "bottom_row_appearance": "Aparência da linha inferior",
    "cards_layout": "Layout de cartões",
    "carousel_navigation": "Navegação em carrossel",
    "carousel_pagination": "Paginação em carrossel",
    "copyright": "Direitos autorais",
    "edit_logo_in_theme_settings": "Editar logo nas [configurações do tema ](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Editar formato do preço nas [configurações do tema ](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Editar estilo da variante nas [configurações do tema ](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "As inscrições adicionam [perfis de cliente](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Para mostrar o botão, o canal de vendas Shop precisa ser instalado e o Shop Pay ativado. [Saiba mais](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Fontes",
    "grid": "Grade",
    "heading_size": "Tamanho do título",
    "image": "Imagem",
    "input": "Entrada",
    "layout": "Layout",
    "link": "Link",
    "link_padding": "Preenchimento do link",
    "localization": "Localização",
    "logo": "Logo",
    "margin": "Margem",
    "media": "Mídias",
    "media_1": "Mídia 1",
    "media_2": "Mídia 2",
    "menu": "Menu",
    "mobile_layout": "Layout em dispositivos móveis",
    "padding": "Preenchimento",
    "padding_desktop": "Preenchimento de desktop",
    "paragraph": "Parágrafo",
    "policies": "Políticas",
    "popup": "Pop-up",
    "search": "Pesquisa",
    "section_layout": "Layout da seção",
    "size": "Tamanho",
    "social_media": "Redes sociais",
    "submit_button": "Botão Enviar",
    "text_presets": "Predefinições de texto",
    "transparent_background": "Fundo transparente",
    "typography_primary": "Tipografia principal",
    "typography_secondary": "Tipografia secundária",
    "typography_tertiary": "Tipografia terciária",
    "mobile_width": "Largura no dispositivo móvel",
    "width": "Largura",
    "images": "Imagens",
    "visibility": "Visibilidade",
    "carousel": "Carrossel",
    "colors": "Cores",
    "collection_page": "Página de coleção",
    "copyright_info": "Saiba como [editar sua declaração de direitos autorais](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Conta de cliente",
    "edit_empty_state_collection_in_theme_settings": "Editar coleção de estado vazio nas [configurações do tema ](/editor?context=theme&category=search)",
    "grid_layout": "Layout de grade",
    "home_page": "Página inicial",
    "inverse_logo_info": "Usado quando o fundo transparente do cabeçalho é definido como Inverso",
    "manage_customer_accounts": "[Gerencie a visibilidade](/admin/settings/customer_accounts) nas configurações de conta de cliente. Contas legadas não aceitas.",
    "manage_policies": "[Gerenciar políticas](/admin/settings/legal)",
    "product_page": "Página do produto",
    "text": "Texto",
    "thumbnails": "Miniaturas",
    "visible_if_collection_has_more_products": "Visível se a coleção tiver mais produtos que os mostrados",
    "app_required_for_ratings": "É necessário um app para classificações de produtos. [Saiba mais](https://help.shopify.com/manual/apps)"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Compartilhe informações sobre a marca com clientes. Descreva um produto, faça comunicados ou dê as boas-vindas aos clientes na loja.</p>"
  },
  "text_defaults": {
    "button_label": "Comprar agora",
    "collapsible_row": "Linha recolhível",
    "heading": "Título",
    "email_signup_button_label": "Assinar",
    "accordion_heading": "Cabeçalho sanfona",
    "contact_form_button_label": "Enviar",
    "popup_link": "Link de pop-up",
    "sign_up": "Criar conta",
    "welcome_to_our_store": "Boas-vindas à nossa loja",
    "be_bold": "Ouse.",
    "shop_our_latest_arrivals": "Conheça as novidades da loja!"
  },
  "info": {
    "video_alt_text": "Descreva o vídeo para usuários de tecnologia assistiva",
    "video_autoplay": "Por padrão, os vídeos serão silenciados",
    "video_external": "Usar URL do YouTube ou do Vimeo",
    "link_info": "Opcional: permite a opção de clicar no ícone",
    "carousel_layout_on_mobile": "O Carrossel é usado em dispositivos móveis",
    "carousel_hover_behavior_not_supported": "O \"Carrossel\" ao passar o cursor não está disponível quando o tipo \"Carrossel\" for selecionado no nível da seção.",
    "checkout_buttons": "Permite que os compradores finalizem a compra mais rápido e pode melhorar a conversão. [Saiba mais](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Título personalizado",
    "edit_presets_in_theme_settings": "Editar predefinições nas [configurações do tema ](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Personalize os filtros com o [app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "grid_layout_on_mobile": "O layout da grade é usado para dispositivos móveis",
    "logo_font": "Aplica-se somente quando não há nenhum logo selecionado",
    "manage_countries_regions": "[Gerenciar países/regiões](/admin/settings/markets)",
    "manage_languages": "[Gerenciar idiomas](/admin/settings/languages)",
    "transparent_background": "Analise cada modelo em que é aplicado o fundo transparente por questões de legibilidade",
    "aspect_ratio_adjusted": "Ajuste feito em alguns layouts",
    "auto_open_cart_drawer": "Ao ativar, o carrinho de compras deslizante será aberto automaticamente quando um produto for adicionado.",
    "custom_liquid": "Adicione snippets de app ou outros códigos do Liquid para criar personalizações avançadas. [Saiba mais](https://shopify.dev/docs/api/liquid)",
    "pills_usage": "Usado para filtros aplicados, códigos de desconto e sugestões de pesquisa",
    "applies_on_image_only": "Aplica-se apenas a imagens",
    "hover_effects": "Aplica-se a cartões de produto e coleção"
  },
  "categories": {
    "basic": "Basic",
    "collection": "Coleção",
    "collection_list": "Lista de coleções",
    "footer": "Rodapé",
    "forms": "Formulários",
    "header": "Cabeçalho",
    "layout": "Layout",
    "links": "Links",
    "product": "Produto",
    "product_list": "Lista de produtos",
    "banners": "Banners",
    "collections": "Coleções",
    "custom": "Personalizado",
    "decorative": "Decorativo",
    "products": "Produtos",
    "other_sections": "Outro",
    "storytelling": "Storytelling"
  }
}

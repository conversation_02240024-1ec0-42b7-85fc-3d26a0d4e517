/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "borders": "Bordi",
    "collapsible_row": "Riga comprimibile",
    "colors": "Colori",
    "custom_section": "Sezione personalizzata",
    "icon": "Icona",
    "logo_and_favicon": "Logo e favicon",
    "overlapping_blocks": "Blocchi sovrapposti",
    "product_buy_buttons": "Pulsanti Acquista",
    "product_description": "Descrizione",
    "product_price": "Prezzo",
    "product_variant_picker": "Selettore di variante",
    "slideshow": "Presentazione",
    "typography": "Caratteri tipografici",
    "video": "Video",
    "slideshow_controls": "Controlli di presentazione",
    "size": "Dimensione",
    "spacing": "Spaziatura",
    "product_recommendations": "Prodotti consigliati",
    "product_media": "Contenuti multimediali prodotto",
    "featured_collection": "Collezione in evidenza",
    "add_to_cart": "Aggiungi al carrello",
    "email_signup": "Iscrizione alla newsletter",
    "submit_button": "Pulsante Invia",
    "grid_layout_selector": "Selettore layout griglia",
    "image": "Immagine",
    "list_items": "Articoli dell'elenco",
    "facets": "Facet",
    "variants": "Varianti",
    "product_cards": "Schede prodotto",
    "styles": "Stili",
    "primary_button": "Pulsante primario",
    "secondary_button": "Pulsante secondario",
    "popovers": "Finestre a comparsa",
    "buttons": "Pulsanti",
    "inputs": "Input",
    "marquee": "Area di selezione",
    "alternating_content_rows": "Righe alternate",
    "pull_quote": "Citazione",
    "contact_form": "Modulo di contatto",
    "featured_product": "Prodotti in evidenza",
    "icons_with_text": "Icone con testo",
    "products_carousel": "Elenco dei prodotti: carosello",
    "products_grid": "Elenco dei prodotti: griglia",
    "product_list": "Elenco dei prodotti",
    "spacer": "Spaziatore",
    "accelerated_checkout": "Check-out veloce",
    "accordion": "Accordion",
    "accordion_row": "riga Accordion",
    "animations": "Animazioni",
    "announcement": "Annuncio",
    "announcement_bar": "Barra degli annunci",
    "badges": "Badge",
    "button": "Pulsante",
    "cart": "Carrello",
    "cart_items": "Articoli nel carrello",
    "cart_products": "Prodotti del carrello",
    "cart_title": "Carrello",
    "collection": "Collezione",
    "collection_card": "Scheda collezioni",
    "collection_columns": "Colonne collezioni",
    "collection_container": "Collezione",
    "collection_description": "Descrizione della collezione",
    "collection_image": "Immagine della collezione",
    "collection_info": "Informazioni sulla collezione",
    "collection_list": "Elenco delle collezioni",
    "collections": "Collezioni",
    "content": "Contenuto",
    "content_grid": "Griglia contenuto",
    "details": "Dettagli",
    "divider": "Divisore",
    "filters": "Filtri e ordinamento",
    "follow_on_shop": "Segui su Shop",
    "footer": "Footer",
    "footer_utilities": "Utility di footer",
    "group": "Gruppo",
    "header": "Header",
    "heading": "Titolo",
    "icons": "Icone",
    "image_with_text": "Immagine con testo",
    "input": "Input",
    "logo": "Logo",
    "magazine_grid": "Griglia riviste",
    "media": "Contenuti multimediali",
    "menu": "Menu",
    "mobile_layout": "Layout dispositivo mobile",
    "payment_icons": "Icone di pagamento",
    "popup_link": "Link pop up",
    "predictive_search": "Popover ricerca",
    "predictive_search_empty": "Ricerca predittiva vuota",
    "price": "Prezzo",
    "product": "Prodotto",
    "product_card": "Scheda prodotto",
    "product_card_media": "Contenuti multimediali",
    "product_card_rendering": "Rendering della scheda prodotto",
    "product_grid": "Griglia",
    "product_grid_main": "Griglia prodotti",
    "product_image": "Immagine del prodotto",
    "product_information": "Informazioni sul prodotto",
    "product_review_stars": "Stelline recensione",
    "quantity": "Quantità",
    "row": "Riga",
    "search": "Ricerca",
    "section": "Sezione",
    "selected_variants": "Varianti selezionate",
    "shop_the_look": "Acquista l'outfit",
    "slide": "Scorrimento",
    "social_media_links": "Link ai social media",
    "steps": "Passaggi",
    "summary": "Riepilogo",
    "swatches": "Campioni di colore",
    "testimonials": "Testimonial",
    "text": "Testo",
    "title": "Titolo",
    "utilities": "Utility",
    "search_input": "Input di ricerca",
    "search_results": "Risultati della ricerca",
    "read_only": "Sola lettura",
    "404": "404",
    "collection_title": "Titolo della collezione",
    "collections_bento": "Elenco delle collezioni: Bento",
    "collections_carousel": "Elenco delle collezioni: carosello",
    "collections_grid": "Elenco delle collezioni: griglia",
    "collection_links": "Link alle collezioni",
    "collection_links_spotlight": "Link alle collezioni: Spotlight",
    "collection_links_text": "Link alle collezioni: testo",
    "count": "Conteggio",
    "divider_section": "Divisore",
    "faq_section": "Domande frequenti",
    "hero": "Hero",
    "jumbo_text": "Testo jumbo",
    "marquee_section": "Area di selezione",
    "media_with_text": "Contenuti multimediali con testo",
    "view_all_button": "Visualizza tutto",
    "video_section": "Video",
    "blog": "Blog",
    "blog_posts": "Articoli del blog",
    "product_title": "Titolo del prodotto",
    "custom_liquid": "Liquid personalizzato",
    "blog_post": "Articolo del blog",
    "caption": "Didascalia",
    "collection_card_image": "Immagine",
    "collections_editorial": "Elenco delle collezioni: editoriale",
    "copyright": "Copyright",
    "drawers": "Finestre",
    "editorial": "Editoriale",
    "editorial_jumbo_text": "Editoriale: testo Jumbo",
    "hero_marquee": "Hero: Marquee",
    "input_fields": "Campi di inserimento",
    "local_pickup": "Ritiro locale",
    "page": "Pagina",
    "page_content": "Contenuto",
    "page_layout": "Layout pagina",
    "policy_list": "Link alle informative",
    "prices": "Prezzi",
    "product_list_button": "Pulsante Visualizza tutto",
    "products_editorial": "Elenco dei prodotti: editoriale",
    "social_link": "Link ai social",
    "split_showcase": "Split Showcase",
    "variant_pickers": "Selettori di varianti",
    "pills": "A pillole",
    "large_logo": "Logo grande",
    "product_inventory": "Scorte prodotti"
  },
  "settings": {
    "alignment": "Allineamento",
    "autoplay": "Riproduzione automatica",
    "background": "Sfondo",
    "border_radius": "Raggio angolo",
    "border_width": "Spessore bordo",
    "borders": "Bordi",
    "bottom_padding": "Spaziatura inferiore",
    "button": "Pulsante",
    "color": "Colore",
    "colors": "Colori",
    "content_alignment": "Allineamento contenuto",
    "content_direction": "Orientamento contenuto",
    "content_position": "Posizione contenuto",
    "cover_image_size": "Dimensioni immagine di copertina",
    "cover_image": "Immagine di copertina",
    "custom_minimum_height": "Altezza minima personalizzata",
    "custom_width": "Larghezza personalizzata",
    "enable_video_looping": "Riproduzione in loop dei video",
    "favicon": "Favicon",
    "font_family": "Famiglia di font",
    "gap": "Divario",
    "geometric_translate_y": "Traslazione geometrica Y",
    "heading": "Titolo",
    "icon": "Icona",
    "image": "Immagine",
    "image_icon": "Icona immagine",
    "image_opacity": "Opacità immagine",
    "image_position": "Posizione immagine",
    "image_ratio": "Proporzioni immagine",
    "label": "Etichetta",
    "line_height": "Altezza linea",
    "link": "Link",
    "layout_gap": "Spazio di layout",
    "make_section_full_width": "Rendi sezione a larghezza intera",
    "minimum_height": "Altezza minima",
    "opacity": "Opacità",
    "overlay_opacity": "Opacità della sovrapposizione",
    "padding": "Spaziatura",
    "primary_color": "Link",
    "product": "Prodotto",
    "section_width": "Larghezza sezione",
    "size": "Taglia",
    "slide_spacing": "Spazio slide",
    "slide_width": "Larghezza slide",
    "slideshow_fullwidth": "Slide a larghezza piena",
    "style": "Stile",
    "text": "Testo",
    "text_case": "Maiuscole/Minuscole",
    "top_padding": "Spaziatura superiore",
    "video": "Video",
    "video_alt_text": "Testo alternativo",
    "video_loop": "Video in loop",
    "video_position": "Posizione del video",
    "width": "Larghezza",
    "z_index": "Indice Z",
    "limit_content_width": "Limita larghezza del contenuto",
    "color_scheme": "Schema colori",
    "inherit_color_scheme": "Importa automaticamente schema colori",
    "product_count": "Conteggio prodotti",
    "product_type": "Tipo di prodotto",
    "content_width": "Larghezza contenuto",
    "collection": "Collezione",
    "enable_sticky_content": "Contenuto fisso su desktop",
    "error_color": "Errore",
    "success_color": "Operazione riuscita",
    "primary_font": "Font primario",
    "secondary_font": "Font secondario",
    "tertiary_font": "Font terziario",
    "columns": "Colonne",
    "items_to_show": "Elementi da mostrare",
    "layout": "Layout",
    "layout_type": "Tipo",
    "show_grid_layout_selector": "Mostra selettore layout griglia",
    "view_more_show": "Mostra pulsante Visualizza altro",
    "image_gap": "Spazio immagine",
    "width_desktop": "Larghezza desktop",
    "width_mobile": "Larghezza dispositivo mobile",
    "border_style": "Stile bordo",
    "height": "Altezza",
    "thickness": "Spessore",
    "stroke": "Tratto",
    "filter_style": "Filtra stile",
    "swatches": "Campioni di colore",
    "quick_add_colors": "Aggiunta rapida colori",
    "divider_color": "Divisore",
    "border_opacity": "Opacità del bordo",
    "hover_background": "Sfondo effetto hover",
    "hover_borders": "Bordi effetto hover",
    "hover_text": "Testo effetto hover",
    "primary_hover_color": "Link effetto hover",
    "primary_button_text": "Testo pulsante primario",
    "primary_button_background": "Sfondo pulsante primario",
    "primary_button_border": "Bordo pulsante primario",
    "secondary_button_text": "Testo pulsante secondario",
    "secondary_button_background": "Sfondo pulsante secondario",
    "secondary_button_border": "Bordo pulsante secondario",
    "shadow_color": "Ombra",
    "mobile_logo_image": "Logo mobile",
    "video_autoplay": "Riproduzione automatica",
    "video_cover_image": "Immagine di copertina",
    "video_external_url": "URL",
    "video_source": "Fonte",
    "first_row_media_position": "Posizione contenuti multimediali su prima riga",
    "card_image_height": "Altezza immagine del prodotto",
    "background_color": "Colore dello sfondo",
    "hide_padding": "Nascondi spaziatura",
    "logo_font": "Font del logo",
    "size_mobile": "Dimensione mobile",
    "pixel_size_mobile": "Dimensione in pixel",
    "percent_size_mobile": "Dimensione in percentuale",
    "unit": "Unità",
    "custom_mobile_size": "Dimensione mobile personalizzata",
    "fixed_height": "Altezza in pixel",
    "fixed_width": "Larghezza in pixel",
    "percent_height": "Altezza in percentuale",
    "percent_width": "Larghezza in percentuale",
    "percent_size": "Dimensione in percentuale",
    "pixel_size": "Dimensione in pixel",
    "accordion": "Accordion",
    "aspect_ratio": "Proporzioni",
    "auto_rotate_announcements": "Ruota annunci automaticamente",
    "auto_rotate_slides": "Ruota slide automaticamente",
    "badge_corner_radius": "Raggio angolo",
    "badge_position": "Posizione sulle schede",
    "badge_sale_color_scheme": "In offerta",
    "badge_sold_out_color_scheme": "Esaurito",
    "behavior": "Comportamento",
    "blur": "Ombra sfumata",
    "border": "Bordo",
    "bottom": "In basso",
    "carousel_on_mobile": "Carosello su dispositivo mobile",
    "cart_count": "Conteggio carrello",
    "cart_items": "Articoli nel carrello",
    "cart_related_products": "Prodotti simili",
    "cart_title": "Carrello",
    "cart_total": "Totale carrello",
    "cart_type": "Tipo",
    "case": "Maiuscole/Minuscole",
    "checkout_buttons": "Pulsanti del check-out veloce",
    "collection_list": "Collezioni",
    "collection_templates": "Modelli per la collezione",
    "content": "Contenuto",
    "corner_radius": "Raggio angolo",
    "country_region": "Paese/Area geografica",
    "currency_code": "Codice valuta",
    "custom_height": "Altezza personalizzata",
    "desktop_height": "Altezza desktop",
    "direction": "Direzione",
    "display": "Schermo",
    "divider_thickness": "Spessore divisore",
    "divider": "Divisore",
    "dividers": "Divisori",
    "drop_shadow": "Ombra discendente",
    "empty_state_collection_info": "Mostrato prima di inserire una ricerca",
    "empty_state_collection": "Collezione con stato vuoto",
    "enable_filtering": "Filtri",
    "enable_grid_density": "Controllo layout griglia",
    "enable_sorting": "Ordinamento",
    "enable_zoom": "Abilita zoom",
    "equal_columns": "Colonne uguali",
    "expand_first_group": "Espandi primo gruppo",
    "extend_media_to_screen_edge": "Estendi contenuti multimediali al bordo dello schermo",
    "extend_summary": "Estendi al bordo dello schermo",
    "extra_large": "Molto grande",
    "extra_small": "Molto piccolo",
    "flag": "Bandiera",
    "font_price": "Font del prezzo",
    "font_weight": "Peso font",
    "font": "Font",
    "full_width_first_image": "Prima immagine a larghezza intera",
    "full_width_on_mobile": "Larghezza intera su dispositivo mobile",
    "heading_preset": "Preconfigurazioni titolo",
    "hide_unselected_variant_media": "Nascondi contenuti multimediali della variante non selezionata",
    "horizontal_gap": "Distanza orizzontale",
    "horizontal_offset": "Scostamento orizzontale ombreggiatura",
    "hover_behavior": "Comportamento al passaggio del mouse",
    "icon_background": "Sfondo icona",
    "icons": "Icone",
    "image_border_radius": "Raggio angolo dell'immagine",
    "installments": "Rate",
    "integrated_button": "Pulsante integrato",
    "language_selector": "Selettore lingua",
    "large": "Grande",
    "left_padding": "Spaziatura a sinistra",
    "left": "Sinistra",
    "letter_spacing": "Spaziatura dei caratteri",
    "limit_media_to_screen_height": "Adatta all'altezza dello schermo",
    "limit_product_details_width": "Limita larghezza dettagli del prodotto",
    "link_preset": "Preconfigurazione link",
    "links": "Link",
    "logo": "Logo",
    "loop": "Loop",
    "make_details_sticky_desktop": "Fisso su desktop",
    "max_width": "Larghezza massima",
    "media_height": "Altezza contenuti multimediali",
    "media_overlay": "Sovrapposizione contenuti multimediali",
    "media_position": "Posizione contenuti multimediali",
    "media_type": "Tipo di contenuti multimediali",
    "media_width": "Larghezza contenuti multimediali",
    "menu": "Menu",
    "mobile_columns": "Colonne su dispositivi mobili",
    "mobile_height": "Altezza su dispositivi mobili",
    "mobile_quick_add": "Aggiunta rapida dispositivi mobili",
    "motion_direction": "Direzione Motion",
    "motion": "Motion",
    "movement_direction": "Direzione movimento",
    "navigation_bar_color_scheme": "Schema di colori della barra di navigazione",
    "navigation_bar": "Barra di navigazione",
    "navigation": "Navigazione",
    "open_new_tab": "Apri link in una nuova scheda",
    "overlay_color": "Colore sovrapposizione",
    "overlay": "Sovrapposizione",
    "padding_bottom": "Spaziatura inferiore",
    "padding_horizontal": "Spaziatura orizzontale",
    "padding_top": "Spaziatura superiore",
    "page_width": "Larghezza pagina",
    "pagination": "Impaginazione",
    "placement": "Posizionamento",
    "position": "Posizione",
    "preset": "Preconfigurazione",
    "product_cards": "Schede prodotto",
    "product_pages": "Pagine del prodotto",
    "product_templates": "Modelli prodotto",
    "products": "Prodotti",
    "quick_add": "Aggiunta rapida",
    "ratio": "Proporzioni",
    "regular": "Normale",
    "review_count": "Conteggio recensioni",
    "right": "Destra",
    "row_height": "Altezza riga",
    "row": "Riga",
    "seller_note": "Consenti nota al venditore",
    "shape": "Forma",
    "show_as_accordion": "Mostra come Accordion su dispositivo mobile",
    "show_sale_price_first": "Mostra prima i prezzi di vendita",
    "show_tax_info": "Dati fiscali",
    "show": "Mostra",
    "small": "Piccolo",
    "speed": "Velocità",
    "statement": "Estratto conto",
    "sticky_header": "Header fisso",
    "text_hierarchy": "Gerarchia testo",
    "text_presets": "Preconfigurazioni del testo",
    "title": "Titolo",
    "top": "In alto",
    "type": "Tipo",
    "type_preset": "Preconfigurazione del testo",
    "underline_thickness": "Spessore sottolineatura",
    "variant_images": "Immagini varianti",
    "vendor": "Venditore",
    "vertical_gap": "Distanza verticale",
    "vertical_offset": "Scostamento verticale ombreggiatura",
    "vertical_on_mobile": "Verticale su dispositivo mobile",
    "view_all_as_last_card": "\"Visualizza tutto\" come ultima scheda",
    "weight": "Peso",
    "wrap": "A capo",
    "read_only": "Sola lettura",
    "always_stack_buttons": "Impila sempre i pulsanti",
    "button_text_case": "Maiuscole/minuscole testo",
    "button_text_weight": "Spessore testo",
    "custom_mobile_width": "Larghezza personalizzata dispositivo mobile",
    "gradient_direction": "Direzione del gradiente",
    "headings": "Intestazioni",
    "horizontal_padding": "Spaziatura interna orizzontale",
    "overlay_style": "Stile sovrapposizione",
    "shadow_opacity": "Opacità ombra",
    "show_filter_label": "Etichette di testo per i filtri applicati",
    "show_swatch_label": "Etichette di testo per i campioni di colore",
    "show_count": "Mostra conteggio",
    "transparent_background": "Sfondo trasparente",
    "vertical_padding": "Spaziatura interna verticale",
    "visibility": "Visibilità",
    "account": "Account",
    "align_baseline": "Allinea la base del testo",
    "add_discount_code": "Consenti sconti nel carrello",
    "background_overlay": "Sovrapposizione sfondo",
    "background_media": "Contenuti multimediali sfondo",
    "border_thickness": "Spessore bordo",
    "bottom_row": "Riga inferiore",
    "card_size": "Dimensioni scheda",
    "auto_open_cart_drawer": "\"Aggiungi al carrello\" apre automaticamente la finestra",
    "collection_count": "Conteggio collezioni",
    "collection_title_case": "Maiuscole/minuscole del titolo della collezione",
    "custom_liquid": "Codice Liquid",
    "default": "Predefinito",
    "default_logo": "Logo predefinito",
    "divider_width": "Larghezza divisore",
    "hide_logo_on_home_page": "Nascondi logo sulla homepage",
    "inverse": "Inverso",
    "inverse_logo": "Logo inverso",
    "layout_style": "Stile",
    "length": "Lunghezza",
    "mobile_card_size": "Dimensioni scheda per dispositivi mobili",
    "mobile_pagination": "Impaginazione per dispositivi mobili",
    "open_row_by_default": "Riga aperta per impostazione predefinita",
    "page": "Pagina",
    "page_transition_enabled": "Transizione pagina",
    "product_and_card_title_case": "Maiuscole/minuscole del titolo del prodotto e della scheda",
    "product_title_case": "Maiuscole/minuscole del titolo del prodotto",
    "right_padding": "Spaziatura interna a destra",
    "search": "Ricerca",
    "search_icon": "Icona di ricerca",
    "search_position": "Posizione",
    "search_row": "Riga",
    "show_author": "Autore",
    "show_alignment": "Mostra allineamento",
    "show_date": "Data",
    "show_pickup_availability": "Mostra disponibilità per il ritiro",
    "show_search": "Mostra ricerca",
    "text_label_case": "Maiuscole/minuscole dell'etichetta di testo",
    "use_inverse_logo": "Usa logo inverso",
    "product_corner_radius": "Raggio angolo prodotto",
    "card_corner_radius": "Raggio angolo scheda",
    "alignment_mobile": "Allineamento su dispositivi mobili",
    "animation_repeat": "Ripeti animazione",
    "blurred_reflection": "Riflesso sfocato",
    "card_hover_effect": "Effetto al passaggio del mouse sulla scheda",
    "effects": "Effetti",
    "inventory_threshold": "Soglia per scorte in esaurimento",
    "reflection_opacity": "Opacità del riflesso",
    "show_inventory_quantity": "Mostra quantità scorte in esaurimento",
    "transition_to_main_product": "Transizione dalla scheda prodotto alla pagina del prodotto"
  },
  "options": {
    "adapt_to_image": "Adatta a immagine",
    "apple": "Mela",
    "arrow": "Freccia",
    "auto": "Automatico",
    "banana": "Banana",
    "bottle": "Bottiglia",
    "box": "Scatola",
    "buttons": "Pulsanti",
    "carrot": "Carota",
    "center": "Al centro",
    "chat_bubble": "Fumetto chat",
    "clipboard": "Blocco appunti",
    "contain": "Limita",
    "counter": "Contatore",
    "cover": "Copertina",
    "custom": "Personalizzato",
    "dairy_free": "Senza latticini",
    "dairy": "Latticini",
    "default": "Predefinito",
    "dropdowns": "Menu a discesa",
    "dots": "Punti",
    "dryer": "Asciugatrice",
    "end": "Alla fine",
    "eye": "Occhio",
    "facebook": "Facebook",
    "fill": "Riempi",
    "fire": "Fuoco",
    "fit": "Adatta",
    "full": "Completo",
    "full_and_page": "Sfondo completo, contenuto a larghezza di pagina",
    "gluten_free": "Senza glutine",
    "heading": "Titolo",
    "heart": "Cuore",
    "horizontal": "Orizzontale",
    "instagram": "Instagram",
    "iron": "Ferro",
    "landscape": "Orizzontale",
    "large": "Grande",
    "leaf": "Foglia",
    "leather": "Pelle",
    "lg": "L",
    "lightning_bolt": "Fulmine",
    "link": "Link",
    "lipstick": "Rossetto",
    "lock": "Lucchetto",
    "lowercase": "minuscolo",
    "m": "M",
    "map_pin": "Pin mappa",
    "medium": "Medio",
    "none": "Nessuno",
    "numbers": "Numeri",
    "nut_free": "Senza frutta a guscio",
    "outline": "Contorno",
    "page": "Pagina",
    "pants": "Pantaloni",
    "paw_print": "Impronta di zampa",
    "pepper": "Pepe",
    "perfume": "Profumi",
    "pinterest": "Pinterest",
    "plane": "Aereo",
    "plant": "Piante",
    "portrait": "Verticale",
    "price_tag": "Cartellino prezzo",
    "question_mark": "Punto interrogativo",
    "recycle": "Riciclo",
    "return": "Reso",
    "ruler": "Righello",
    "s": "S",
    "sentence": "Frase",
    "serving_dish": "Piatto da portata",
    "shirt": "Maglietta",
    "shoe": "Scarpa",
    "silhouette": "Silhouette",
    "small": "Piccola",
    "snapchat": "Snapchat",
    "snowflake": "Fiocco di neve",
    "solid": "Pieno",
    "space_between": "Spazio in mezzo",
    "square": "Quadrato",
    "star": "Stella",
    "start": "All'inizio",
    "stopwatch": "Cronometro",
    "tiktok": "TikTok",
    "truck": "Camion",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "uppercase": "Maiuscolo",
    "vertical": "Verticale",
    "vimeo": "Vimeo",
    "washing": "Lavaggio",
    "circle": "Tondo",
    "swatches": "Campioni di colore",
    "full_and_page_offset_left": "Sfondo completo, contenuto a larghezza di pagina, scostamento a sinistra",
    "full_and_page_offset_right": "Sfondo completo, contenuto a larghezza di pagina, scostamento a destra",
    "offset_left": "Scostamento a sinistra",
    "offset_right": "Scostamento a destra",
    "page_center_aligned": "Pagina, allineamento centrale",
    "page_left_aligned": "Pagina, allineamento a sinistra",
    "page_right_aligned": "Pagina, allineamento a destra",
    "button": "Pulsante",
    "caption": "Didascalia",
    "h1": "Titolo 1",
    "h2": "Titolo 2",
    "h3": "Titolo 3",
    "h4": "Titolo 4",
    "h5": "Titolo 5",
    "h6": "Titolo 6",
    "paragraph": "Paragrafo",
    "primary": "Primario",
    "secondary": "Secondario",
    "tertiary": "Terziario",
    "chevron_left": "Parentesi ad angolo a sinistra",
    "chevron_right": "Parentesi ad angolo a destra",
    "diamond": "Diamante",
    "grid": "Griglia",
    "parallelogram": "Parallelogramma",
    "rounded": "Arrotondati",
    "fit_content": "Vestibilità",
    "pills": "\"A pillole\"",
    "heavy": "Marcato",
    "thin": "Fine",
    "drawer": "Finestra",
    "preview": "Anteprima",
    "text": "Testo",
    "video_uploaded": "Caricato",
    "video_external_url": "URL esterno",
    "up": "Su",
    "down": "Giù",
    "gradient": "Gradiente",
    "aspect_ratio": "Proporzioni",
    "fixed": "Fisso",
    "pixel": "Pixel",
    "percent": "Percentuale",
    "above_carousel": "Sopra il carosello",
    "all": "Tutto",
    "always": "Sempre",
    "arrows_large": "Frecce grandi",
    "arrows": "Frecce",
    "balance": "Saldo",
    "bento": "Bento",
    "black": "Nero",
    "bluesky": "Bluesky",
    "body_large": "Testo (grande)",
    "body_regular": "Testo (normale)",
    "body_small": "Testo (piccolo)",
    "bold": "Grassetto",
    "bottom_left": "In basso a sinistra",
    "bottom_right": "In basso a destra",
    "bottom": "In basso",
    "capitalize": "Maiuscole",
    "caret": "Accento circonflesso",
    "carousel": "Carosello",
    "check_box": "Casella di controllo",
    "chevron_large": "Parentesi ad angolo grandi",
    "chevron": "Parentesi ad angolo",
    "chevrons": "Parentesi ad angolo",
    "classic": "Classico",
    "collection_images": "Immagini collezione",
    "color": "Colore",
    "complementary": "Complementare",
    "dissolve": "Dissolvi",
    "dotted": "Punteggiato",
    "editorial": "Editoriale",
    "extra_large": "Molto grande",
    "extra_small": "Molto piccolo",
    "featured_collections": "Collezioni in evidenza",
    "featured_products": "Prodotti in evidenza",
    "font_primary": "Primario",
    "font_secondary": "Secondario",
    "font_tertiary": "Terziario",
    "forward": "Avanti",
    "full_screen": "Schermo intero",
    "heading_extra_large": "Titolo (molto grande)",
    "heading_extra_small": "Titolo (molto piccolo)",
    "heading_large": "Titolo (grande)",
    "heading_regular": "Titolo (normale)",
    "heading_small": "Titolo (piccolo)",
    "icon": "Icona",
    "image": "Immagine",
    "input": "Input",
    "inside_carousel": "Carosello interno",
    "inverse_large": "Inverso grande",
    "inverse": "Inverso",
    "large_arrows": "Frecce grandi",
    "large_chevrons": "Parentesi ad angolo grandi",
    "left": "Sinistra",
    "light": "Leggero",
    "linkedin": "LinkedIn",
    "loose": "Lento",
    "media_first": "Contenuti multimediali primi",
    "media_second": "Contenuti multimediali secondi",
    "modal": "Modale",
    "narrow": "Stretto",
    "never": "Mai",
    "next_to_carousel": "Accanto al carosello",
    "normal": "Normale",
    "nowrap": "Nessun a capo",
    "off_media": "Fuori dai contenuti multimediali",
    "on_media": "Sui contenuti multimediali",
    "on_scroll_up": "Durante lo scorrimento verso l'alto",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "A pillola",
    "plus": "Plus",
    "pretty": "Pretty",
    "price": "Prezzo",
    "primary_style": "Stile primario",
    "rectangle": "Rettangolo",
    "regular": "Normale",
    "related": "Correlato",
    "reverse": "Inverso",
    "rich_text": "Rich text",
    "right": "Destra",
    "secondary_style": "Stile secondario",
    "semibold": "Semigrassetto",
    "shaded": "Ombreggiato",
    "show_second_image": "Mostra seconda immagine",
    "single": "Singola",
    "slide_left": "Scorri verso sinistra",
    "slide_up": "Scorri in alto",
    "spotify": "Spotify",
    "stack": "Pila",
    "text_only": "Solo testo",
    "threads": "Threads",
    "thumbnails": "Miniature",
    "tight": "Stretto",
    "top_left": "In alto a sinistra",
    "top_right": "In alto a destra",
    "top": "In alto",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Sottolineato",
    "video": "Video",
    "wide": "Largo",
    "youtube": "YouTube",
    "below_image": "Sotto l'immagine",
    "button_primary": "Pulsante primario",
    "button_secondary": "Pulsante secondario",
    "hidden": "Nascosto",
    "on_image": "Sull'immagine",
    "spotlight": "In evidenza",
    "compact": "Compatto",
    "standard": "Standard",
    "accent": "Elemento decorativo",
    "body": "Corpo",
    "crop_to_fit": "Ritaglia per adattare",
    "hint": "Suggerimento",
    "maintain_aspect_ratio": "Mantieni proporzioni",
    "off": "Disattivato",
    "social_bluesky": "Social: Bluesky",
    "social_facebook": "Social: Facebook",
    "social_instagram": "Social: Instagram",
    "social_linkedin": "Social: LinkedIn",
    "social_pinterest": "Social: Pinterest",
    "social_snapchat": "Social: Snapchat",
    "social_spotify": "Social: Spotify",
    "social_threads": "Social: Threads",
    "social_tiktok": "Social: TikTok",
    "social_tumblr": "Social: Tumblr",
    "social_twitter": "Social: X (Twitter)",
    "social_whatsapp": "Social: WhatsApp",
    "social_vimeo": "Social: Vimeo",
    "social_youtube": "Social: YouTube",
    "subheading": "Sottotitolo",
    "blur": "Sfocato",
    "lift": "Solleva",
    "reveal": "Rivela",
    "scale": "Ridimensiona",
    "subtle_zoom": "Zoom"
  },
  "content": {
    "advanced": "Avanzato",
    "background_image": "Immagine di sfondo",
    "background_video": "Video in background",
    "block_size": "Dimensione blocco",
    "borders": "Bordi",
    "describe_the_video_for": "Descrivi il video per i clienti che utilizzano i lettori di schermo. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "section_size": "Dimensione sezione",
    "slideshow_width": "Larghezza slide",
    "typography": "Caratteri tipografici",
    "width_is_automatically_optimized": "Larghezza automaticamente ottimizzata per i dispositivi mobili.",
    "complementary_products": "I prodotti complementari devono essere configurati tramite l'app Search & Discovery. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "Le colonne verranno ottimizzate automaticamente per i dispositivi mobili",
    "content_width": "La larghezza del contenuto si applica solo quando la larghezza della sezione è impostata come intera larghezza.",
    "adjustments_affect_all_content": "Si applica all'intero contenuto di questo blocco",
    "responsive_font_sizes": "Le dimensioni si adattano automaticamente a tutte le dimensioni dello schermo",
    "buttons": "Pulsanti",
    "swatches": "Campioni di colore",
    "variant_settings": "Impostazioni relative alla variante",
    "background": "Sfondo",
    "cards_layout": "Layout schede",
    "section_layout": "Layout sezione",
    "mobile_size": "Dimensione mobile",
    "appearance": "Aspetto",
    "arrows": "Frecce",
    "body_size": "Dimensioni del testo",
    "bottom_row_appearance": "Aspetto della riga inferiore",
    "carousel_navigation": "Navigazione carosello",
    "carousel_pagination": "Impaginazione carosello",
    "copyright": "Copyright",
    "edit_logo_in_theme_settings": "Modifica il logo in [impostazioni tema](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Modifica la formattazione dei prezzi in [impostazioni tema](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Modifica lo stile della variante in [impostazioni tema](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Aggiunta di iscrizioni [profili cliente](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Affinché il pulsante venga visualizzato, il canale Shop deve essere installato e Shop Pay attivato. [Maggiori informazioni](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Font",
    "grid": "Griglia",
    "heading_size": "Dimensione titolo",
    "image": "Immagine",
    "input": "Input",
    "layout": "Layout",
    "link": "Link",
    "link_padding": "Spaziatura link",
    "localization": "Localizzazione",
    "logo": "Logo",
    "margin": "Margine",
    "media": "Contenuti multimediali",
    "media_1": "Contenuti multimediali 1",
    "media_2": "Contenuti multimediali 2",
    "menu": "Menu",
    "mobile_layout": "Layout dispositivo mobile",
    "padding": "Spaziatura",
    "padding_desktop": "Spaziatura desktop",
    "paragraph": "Paragrafo",
    "policies": "Politiche",
    "popup": "Pop up",
    "search": "Ricerca",
    "size": "Dimensione",
    "social_media": "Social media",
    "submit_button": "Pulsante Invia",
    "text_presets": "Preconfigurazioni del testo",
    "transparent_background": "Sfondo trasparente",
    "typography_primary": "Caratteri tipografici primari",
    "typography_secondary": "Caratteri tipografici secondari",
    "typography_tertiary": "Caratteri tipografici terziari",
    "mobile_width": "Larghezza dispositivo mobile",
    "width": "Larghezza",
    "images": "Immagini",
    "visible_if_collection_has_more_products": "Visibile se la collezione contiene più prodotti di quelli mostrati",
    "carousel": "Carosello",
    "colors": "Colori",
    "collection_page": "Pagina di collezione",
    "copyright_info": "Scopri come [modificare l'informativa sul copyright](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Account cliente",
    "edit_empty_state_collection_in_theme_settings": "Modifica collezione con stato vuoto in [impostazioni del tema](/editor?context=theme&category=search)",
    "grid_layout": "Layout a griglia",
    "home_page": "Homepage",
    "inverse_logo_info": "Utilizzato quando lo sfondo trasparente dell’header è impostato su Inverso",
    "manage_customer_accounts": "[Gestisci la visibilità](/admin/settings/customer_accounts) nelle impostazioni dell'account cliente. Gli account legacy non sono supportati.",
    "manage_policies": "[Gestisci informative](/admin/settings/legal)",
    "product_page": "Pagina del prodotto",
    "text": "Testo",
    "thumbnails": "Miniature",
    "visibility": "Visibilità",
    "app_required_for_ratings": "Per le valutazioni dei prodotti è necessaria un'app. [Maggiori informazioni](https://help.shopify.com/manual/apps)"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Condividi informazioni sul tuo brand con i clienti. Descrivi un prodotto, condividi gli annunci o dai il benvenuto ai clienti nel tuo negozio.</p>"
  },
  "text_defaults": {
    "button_label": "Acquista ora",
    "collapsible_row": "Riga comprimibile",
    "heading": "Titolo",
    "email_signup_button_label": "Iscriviti",
    "accordion_heading": "Titolo Accordion",
    "contact_form_button_label": "Invia",
    "popup_link": "Link pop up",
    "sign_up": "Registrati",
    "welcome_to_our_store": "Ti diamo il benvenuto nel nostro negozio",
    "be_bold": "Sii audace.",
    "shop_our_latest_arrivals": "Scopri gli ultimi arrivi."
  },
  "info": {
    "carousel_layout_on_mobile": "Carosello utilizzato su dispositivo mobile",
    "link_info": "Facoltativo: rende l'icona selezionabile",
    "video_alt_text": "Descrivi il video per gli utenti che utilizzano tecnologie assistive",
    "video_autoplay": "L'audio del video verrà disattivato per impostazione predefinita",
    "video_external": "Utilizza un URL YouTube o Vimeo",
    "carousel_hover_behavior_not_supported": "L'effetto hover \"Carosello\" non è supportato quando il tipo \"Carosello\" è selezionato a livello di sezione",
    "grid_layout_on_mobile": "Layout griglia utilizzato per dispositivi mobili",
    "logo_font": "Si applica solo quando nessun logo è selezionato",
    "checkout_buttons": "Consente agli acquirenti di effettuare il check-out più velocemente e può migliorare la conversione. [Maggiori informazioni](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Titolo personalizzato",
    "edit_presets_in_theme_settings": "Modifica le preconfigurazioni in [impostazioni tema](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Personalizza i filtri con l'[app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "manage_countries_regions": "[Gestisci paesi/aree geografiche](/admin/settings/markets)",
    "manage_languages": "[Gestisci lingue](/admin/settings/languages)",
    "transparent_background": "Esamina ogni modello in cui viene applicato uno sfondo trasparente per la leggibilità",
    "aspect_ratio_adjusted": "Adattate in alcuni layout",
    "auto_open_cart_drawer": "Se abilitata, quando un prodotto viene aggiunto al carrello la finestra del carrello si apre automaticamente.",
    "custom_liquid": "Aggiungi snippet di app o altro codice per creare personalizzazioni avanzate. [Maggiori informazioni](https://shopify.dev/docs/api/liquid)",
    "pills_usage": "Utilizzato per filtri applicati, codici sconto e suggerimenti di ricerca",
    "applies_on_image_only": "Si applica solo alle immagini",
    "hover_effects": "Si applica alle schede di prodotti e collezioni"
  },
  "categories": {
    "product_list": "Elenco dei prodotti",
    "basic": "Base",
    "collection": "Collezione",
    "collection_list": "Elenco delle collezioni",
    "footer": "Footer",
    "forms": "Moduli",
    "header": "Header",
    "layout": "Layout",
    "links": "Link",
    "product": "Prodotto",
    "decorative": "Decorativo",
    "banners": "Banner",
    "collections": "Collezioni",
    "custom": "Personalizzato",
    "products": "Prodotti",
    "other_sections": "Altro",
    "storytelling": "Storytelling"
  }
}

/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "title": {
          "type": "text",
          "name": "t:names.title",
          "disabled": true,
          "settings": {
            "text": "<h2>{{ closest.page.title }}</h2>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "content": {
          "type": "text",
          "name": "t:names.content",
          "disabled": true,
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "100%",
            "max_width": "narrow",
            "alignment": "center",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        }
      },
      "block_order": [
        "title",
        "content"
      ],
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 0
      }
    },
    "form": {
      "type": "section",
      "blocks": {
        "contact_form_info_zH4r8t": {
          "type": "contact-form-info",
          "name": "Contact Form with Info",
          "settings": {
            "contact_heading": "טופס לביטול עסקה",
            "contact_subheading": "לביטול עסקה, אנא מלאו את טופס ביטול העסקה כאן.  לאחר קבלת הבקשה, נבדוק אותה בהתאם לתנאי הביטול ונשיב בהקדם האפשרי.",
            "address": "<span class=\"color-text\">פנו אלינו בדוא\"ל בכתובת</span> <a href=\"mailto:<EMAIL>\"><EMAIL></a>",
            "phone": "",
            "email": "<span class=\"color-text\">תוכלו גם להשיג אותנו בטלפון</span> <a href=\"tel:+9483*\">9483*</a>",
            "whatsapp": "<span class=\"color-text\">אנחנו זמינים גם בוואטסאפ</span> <a href=\"https://api.whatsapp.com/send?phone=972523017348\">0523017348</a>",
            "button_text1": "",
            "button_link1": "shopify://pages/faqs",
            "address_icon": "shopify://shop_images/Icons.svg",
            "phone_icon": "shopify://shop_images/Icons_1.svg",
            "email_icon": "shopify://shop_images/Icons_2.svg",
            "whatsapp_icon": "shopify://shop_images/Icons.png",
            "icon_size": 24,
            "form_title": "שלחו לנו הודעה",
            "form_subtitle": "מלאו את הטופס ואחד מנציגינו יחזור אליכם בהקדם עם כל המידע שאתם צריכים.",
            "button_text": "שליחה",
            "success_message": "תודה על פנייתך! נחזור אליך בהקדם.",
            "heading_size": 48,
            "text_size": 16,
            "heading_color": "#323438",
            "text_color": "#323438",
            "icon_color": "#007a73",
            "form_title_size": 24,
            "form_background_color": "#f8f8f5",
            "form_border_color": "#e6e6e6",
            "form_title_color": "#323438",
            "label_color": "#333333",
            "form_border_radius": 8,
            "input_background_color": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#cccccc",
            "input_focus_border_color": "#000f9f",
            "input_border_radius": 4,
            "button_color": "#007a73",
            "button_text_color": "#ffffff",
            "button_hover_color": "#007a73",
            "button_border_radius": 4,
            "success_color": "#008060",
            "error_color": "#d82c0d"
          },
          "blocks": {}
        }
      },
      "block_order": [
        "contact_form_info_zH4r8t"
      ],
      "custom_css": [
        "{direction: rtl;}"
      ],
      "name": "t:names.contact_form",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 32,
        "section_width": "page-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-1",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 32,
        "padding-block-end": 32,
        "custom_css_class": ""
      }
    }
  },
  "order": [
    "main",
    "form"
  ]
}

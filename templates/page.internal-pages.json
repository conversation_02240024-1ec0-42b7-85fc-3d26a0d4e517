/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "text_A8F3Wr": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<p>{{ closest.page.title }}</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "h1",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "text_KMwtTr": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_A8F3Wr",
        "text_KMwtTr"
      ],
      "disabled": true,
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 80
      }
    },
    "internal_page_layout_section_FTrXwJ": {
      "type": "internal-page-layout-section",
      "blocks": {
        "link_wMYGwX": {
          "type": "link",
          "settings": {
            "link_label": "כללי",
            "link": "#1"
          }
        },
        "link_3aiMPt": {
          "type": "link",
          "settings": {
            "link_label": "מטרות השימוש במידע",
            "link": "#2"
          }
        },
        "link_bYdnaF": {
          "type": "link",
          "settings": {
            "link_label": "העברת מידע",
            "link": "#3"
          }
        },
        "link_JakBQU": {
          "type": "link",
          "settings": {
            "link_label": "אמצעי קשר, פרסומות ודיוור ישיר",
            "link": "#4"
          }
        },
        "link_TiCJCU": {
          "type": "link",
          "settings": {
            "link_label": "קוקיות (Cookies) ואיסוף מידע אוטומטי באתר",
            "link": "#5"
          }
        },
        "link_CdbUhY": {
          "type": "link",
          "settings": {
            "link_label": "אבטחת מידע והצפנה",
            "link": "#6"
          }
        },
        "link_rKJwV9": {
          "type": "link",
          "settings": {
            "link_label": "עיון במידע",
            "link": "#7"
          }
        },
        "link_fJA6K8": {
          "type": "link",
          "settings": {
            "link_label": "מחיקת מידע",
            "link": "#8"
          }
        }
      },
      "block_order": [
        "link_wMYGwX",
        "link_3aiMPt",
        "link_bYdnaF",
        "link_JakBQU",
        "link_TiCJCU",
        "link_CdbUhY",
        "link_rKJwV9",
        "link_fJA6K8"
      ],
      "name": "IntPage Layout privacy-policy",
      "settings": {
        "override_title": "{{ page.title }}",
        "updated_text": "עדכון אחרון: 12/01/2025",
        "sidebar_title": ""
      }
    },
    "internal_page_layout_section_BEyJGp": {
      "type": "internal-page-layout-section",
      "blocks": {
        "link_Tn87YV": {
          "type": "link",
          "settings": {
            "link_label": "תקנון האתר ותנאי השימוש",
            "link": "#1"
          }
        },
        "link_tgpEKD": {
          "type": "link",
          "settings": {
            "link_label": "מדיניות הפרטיות",
            "link": "#2"
          }
        },
        "link_RMaVN7": {
          "type": "link",
          "settings": {
            "link_label": "הצהרת נגישות",
            "link": "#2"
          }
        }
      },
      "block_order": [
        "link_Tn87YV",
        "link_tgpEKD",
        "link_RMaVN7"
      ],
      "name": "IntPage Layout terms",
      "settings": {
        "override_title": "{{ page.title }}",
        "updated_text": "עדכון אחרון: 12/01/2025",
        "sidebar_title": ""
      }
    }
  },
  "order": [
    "main",
    "internal_page_layout_section_FTrXwJ",
    "internal_page_layout_section_BEyJGp"
  ]
}

/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "text_A8F3Wr": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<p>{{ closest.page.title }}</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "h1",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "text_KMwtTr": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_A8F3Wr",
        "text_KMwtTr"
      ],
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 80
      }
    }
  },
  "order": [
    "main"
  ]
}
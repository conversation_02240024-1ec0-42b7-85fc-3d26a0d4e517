

<div
  class="accordion accordion--{{ block.id }} accordion--{{ block.settings.icon }} border-style spacing-style{% if block.settings.inherit_color_scheme == false %} color-{{ block.settings.color_scheme }}{% endif %}{% if block.settings.dividers %} accordion--dividers{% endif %}"
  style="
    --summary-font-family: var(--font-{{ block.settings.type_preset }}--family);
     --summary-font-style: var(--font-{{ block.settings.type_preset }}--style);
     --summary-font-weight: var(--font-{{ block.settings.type_preset }}--weight);
     --summary-font-size: var(--font-{{ block.settings.type_preset }}--size);
     --summary-font-line-height: var(--font-{{ block.settings.type_preset }}--line-height);
     --summary-font-case: var(--font-{{ block.settings.type_preset }}--case);
    {% render 'spacing-style', settings: block.settings %}
    {% render 'border-override', settings: block.settings %}
  "
  {{ block.shopify_attributes }}
>
  {% content_for 'blocks' %}
</div>

{% stylesheet %}
  .accordion {
    flex: 1;
    width: 100%;
  }
  @container style(--border-width: 0) or style(--border-style: none) {
    .accordion--dividers:not([class*='color-']) accordion-custom:first-child .details {
      border-block-start: var(--style-border-width) solid var(--color-border);
    }

    .accordion--dividers:not([class*='color-']) accordion-custom:last-child .details {
      border-block-end: var(--style-border-width) solid var(--color-border);
    }
  }

  .accordion--dividers accordion-custom:not(:first-child) .details {
    border-block-start: var(--style-border-width) solid var(--color-border);
  }

  .accordion--dividers .details-content {
    padding-block-end: var(--padding-sm);
  }

  .accordion--caret .icon-plus,
  .accordion--plus .icon-caret {
    display: none;
  }

  /* because we can't pass apply a specific class on a block based on its parent block setting */
  .accordion .details__header {
    font-family: var(--summary-font-family);
    font-style: var(--summary-font-style);
    font-weight: var(--summary-font-weight);
    font-size: var(--summary-font-size);
    line-height: var(--summary-font-line-height);
    text-transform: var(--summary-font-case);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.accordion",
  "tag": null,
  "class": "accordion",
  "blocks": [
    {
      "type": "_accordion-row"
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "icon",
      "label": "t:settings.icon",
      "options": [
        {
          "value": "caret",
          "label": "t:options.caret"
        },
        {
          "value": "plus",
          "label": "t:options.plus"
        }
      ],
      "default": "caret"
    },
    {
      "type": "checkbox",
      "id": "dividers",
      "label": "t:settings.dividers",
      "default": true
    },
    {
      "type": "select",
      "id": "type_preset",
      "label": "t:settings.heading_preset",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "paragraph",
          "label": "t:options.paragraph"
        },
        {
          "value": "h1",
          "label": "t:options.h1"
        },
        {
          "value": "h2",
          "label": "t:options.h2"
        },
        {
          "value": "h3",
          "label": "t:options.h3"
        },
        {
          "value": "h4",
          "label": "t:options.h4"
        },
        {
          "value": "h5",
          "label": "t:options.h5"
        },
        {
          "value": "h6",
          "label": "t:options.h6"
        }
      ],
      "default": "h6",
      "info": "t:info.edit_presets_in_theme_settings"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "header",
      "content": "t:content.borders"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.style",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "t:settings.thickness",
      "default": 1,
      "visible_if": "{{ block.settings.border != \"none\" }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.opacity",
      "default": 100,
      "visible_if": "{{ block.settings.border != \"none\" }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.accordion",
      "category": "t:categories.layout",
      "blocks": {
        "row-1": {
          "type": "_accordion-row",
          "settings": {
            "open_by_default": true,
            "heading": "Return policy"
          },
          "blocks": {
            "text-1": {
              "type": "text",
              "settings": {
                "text": "<p>Our goal is for every customer to be totally satisfied with their purchase. If this isn't the case, let us know and we'll do our best to work with you to make it right.</p>",
                "width": "100%"
              }
            }
          },
          "block_order": ["text-1"]
        },
        "row-2": {
          "type": "_accordion-row",
          "settings": {
            "heading": "Shipping"
          },
          "blocks": {
            "text-1": {
              "type": "text",
              "settings": {
                "text": "<p>We will work quickly to ship your order as soon as possible. Once your order has shipped, you will receive an email with further information. Delivery times vary depending on your location.</p>",
                "width": "100%"
              }
            }
          },
          "block_order": ["text-1"]
        },
        "row-3": {
          "type": "_accordion-row",
          "settings": {
            "heading": "Manufacturing"
          },
          "blocks": {
            "text-1": {
              "type": "text",
              "settings": {
                "text": "<p>Our products are manufactured both locally and globally. We carefully select our manufacturing partners to ensure our products are high quality and a fair value.</p>",
                "width": "100%"
              }
            }
          },
          "block_order": ["text-1"]
        }
      },
      "block_order": ["row-1", "row-2", "row-3"]
    }
  ]
}
{% endschema %}

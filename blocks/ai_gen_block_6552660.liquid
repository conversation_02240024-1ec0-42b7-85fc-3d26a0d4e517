{% doc %}
  @prompt
    image with text and subtext, text and subtext should be on image bottom side, add a blur background on text and subtext div. on hover the subtext will be visible 
    

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-image-text-overlay-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-image-text-overlay__image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.image_height }}px;
    display: block;
  }

  .ai-image-text-overlay__image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .ai-image-text-overlay__placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-image-text-overlay__placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 200px;
    max-height: 200px;
    opacity: 0.3;
  }

  .ai-image-text-overlay__content-{{ ai_gen_id }} {
    position: absolute;
    left: 0;
    right: 0;
    padding: {{ block.settings.content_padding }}px;
    background: {{ block.settings.overlay_color }};
    backdrop-filter: blur({{ block.settings.blur_intensity }}px);
    -webkit-backdrop-filter: blur({{ block.settings.blur_intensity }}px);
    color: {{ block.settings.text_color }};
    cursor: pointer;
    transition: all 0.3s ease;
    bottom: 15px;
    width: 95%;
    border-radius: 10px;
    margin: 0 auto;
  }

  .ai-image-text-overlay__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .ai-image-text-overlay__subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    margin: 0;
    opacity: 0;
    line-height: 1.4;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .ai-image-text-overlay-{{ ai_gen_id }}:hover .ai-image-text-overlay__subtitle-{{ ai_gen_id }} {
    opacity: 1;
    max-height: 200px;
    margin-top: 8px;
  }

  .ai-image-text-overlay__content-{{ ai_gen_id }}:hover {
    backdrop-filter: blur({{ block.settings.blur_intensity | times: 1.5 }}px);
    -webkit-backdrop-filter: blur({{ block.settings.blur_intensity | times: 1.5 }}px);
  }

  @media screen and (max-width: 749px) {
    .ai-image-text-overlay__image-wrapper-{{ ai_gen_id }} {
      height: {{ block.settings.image_height | times: 1 }}px;
    }

    .ai-image-text-overlay__title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_size | times: 0.85 }}px;
    }

    .ai-image-text-overlay__subtitle-{{ ai_gen_id }} {
      font-size: {{ block.settings.subtitle_size | times: 0.85 }}px;
    }

    .ai-image-text-overlay__content-{{ ai_gen_id }} {
      padding: {{ block.settings.content_padding | times: 0.75 }}px;
    }

    .ai-image-text-overlay__content-{{ ai_gen_id }} {
      cursor: default;
    }

    .ai-image-text-overlay__subtitle-{{ ai_gen_id }} {
      opacity: 1;
      max-height: none;
      margin-top: 8px;
    }
  }

  @media (hover: none) {
    .ai-image-text-overlay__subtitle-{{ ai_gen_id }} {
      opacity: 1;
      max-height: none;
      margin-top: 8px;
    }
  }
{% endstyle %}

<div
  class="ai-image-text-overlay-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-image-text-overlay__image-wrapper-{{ ai_gen_id }}">
    {% if block.settings.image %}
      <img
        src="{{ block.settings.image | image_url: width: 1200 }}"
        alt="{{ block.settings.image.alt | escape }}"
        class="ai-image-text-overlay__image-{{ ai_gen_id }}"
        loading="lazy"
        width="{{ block.settings.image.width }}"
        height="{{ block.settings.image.height }}"
      >
    {% else %}
      <div class="ai-image-text-overlay__placeholder-{{ ai_gen_id }}">
        {{ 'image' | placeholder_svg_tag }}
      </div>
    {% endif %}

    {% if block.settings.title != blank or block.settings.subtitle != blank %}
      <div class="ai-image-text-overlay__content-{{ ai_gen_id }}">
        {% if block.settings.title != blank %}
          <h2 class="ai-image-text-overlay__title-{{ ai_gen_id }}">
            {{ block.settings.title }}
          </h2>
        {% endif %}

        {% if block.settings.subtitle != blank %}
          <p class="ai-image-text-overlay__subtitle-{{ ai_gen_id }}">
            {{ block.settings.subtitle }}
          </p>
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Image with Text Overlay",
  "settings": [
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 200,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Image height",
      "default": 400
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Your Title Here"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Add your subtitle or description text here"
    },
    {
      "type": "range",
      "id": "content_padding",
      "min": 16,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Content padding",
      "default": 32
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Colors & Effects"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay color",
      "default": "rgba(0,0,0,0.4)"
    },
    {
      "type": "range",
      "id": "blur_intensity",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Blur intensity",
      "default": 8
    }
  ],
  "presets": [
    {
      "name": "Image with Text Overlay"
    }
  ]
}
{% endschema %}
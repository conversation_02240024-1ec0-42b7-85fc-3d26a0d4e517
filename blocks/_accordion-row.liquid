

<accordion-custom
  {% if block.settings.open_by_default %}
    open-by-default-on-desktop
    open-by-default-on-mobile
  {% endif %}
>
  <details
    class="details"
    {{ block.shopify_attributes }}
  >
    <summary class="details__header">
      {{ block.settings.heading }}
      <span class="svg-wrapper icon-caret icon-animated">
        {{- 'icon-caret.svg' | inline_asset_content -}}
      </span>
      <span class="svg-wrapper icon-plus">
        {{- 'icon-plus.svg' | inline_asset_content -}}
      </span>
    </summary>

    <div class="details-content">
      {% content_for 'blocks' %}
    </div>
  </details>
</accordion-custom>

{% schema %}
{
  "name": "t:names.accordion_row",
  "tag": null,
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "t:settings.heading",
      "default": "t:text_defaults.accordion_heading"
    },
    {
      "type": "checkbox",
      "id": "open_by_default",
      "label": "t:settings.open_row_by_default",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:names.accordion_row",
      "blocks": {
        "text-1": {
          "type": "text",
          "settings": {
            "text": "<p>We will work quickly to ship your order as soon as possible. Once your order has shipped, you will receive an email with further information. Delivery times vary depending on your location.</p>",
            "width": "100%"
          }
        }
      },
      "block_order": ["text-1"],
      "settings": {
        "heading": "When will I get my order?"
      }
    }
  ]
}
{% endschema %}

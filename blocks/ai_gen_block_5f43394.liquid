{% doc %}
  @prompt
    Create a product carousel section with navigation arrows for desktop and a progress bar at the bottom. The carousel should display products in a horizontal scrollable layout with left and right arrow controls for desktop users. Include a progress indicator bar below the products that shows the current position in the carousel. The design should be responsive and match modern e-commerce styling with smooth transitions.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-product-carousel-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .ai-product-carousel__header-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 30px;
  }

  .ai-product-carousel__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.text_color }};
    margin: 0 0 10px;
  }

  .ai-product-carousel__subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    color: {{ block.settings.text_color }};
    opacity: 0.8;
    margin: 0;
  }

  .ai-product-carousel__container-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-product-carousel__track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.4s ease;
    gap: {{ block.settings.product_gap }}px;
  }

  .ai-product-carousel__slide-{{ ai_gen_id }} {
    flex: 0 0 calc((100% - {{ block.settings.product_gap | times: 3 }}px) / {{ block.settings.products_per_row }});
    background: {{ block.settings.card_background }};
    border-radius: {{ block.settings.card_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .ai-product-carousel__slide-{{ ai_gen_id }}:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .ai-product-carousel__image-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
  }

  .ai-product-carousel__image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .ai-product-carousel__slide-{{ ai_gen_id }}:hover .ai-product-carousel__image-{{ ai_gen_id }} {
    transform: scale(1.05);
  }

  .ai-product-carousel__image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-product-carousel__image-placeholder-{{ ai_gen_id }} svg {
    width: 60%;
    height: 60%;
    opacity: 0.3;
  }

  .ai-product-carousel__content-{{ ai_gen_id }} {
    padding: 16px;
  }

  .ai-product-carousel__product-title-{{ ai_gen_id }} {
    font-size: 16px;
    font-weight: 600;
    color: {{ block.settings.text_color }};
    margin: 0 0 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .ai-product-carousel__product-price-{{ ai_gen_id }} {
    font-size: 18px;
    font-weight: 700;
    color: {{ block.settings.price_color }};
    margin: 0;
  }

  .ai-product-carousel__nav-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: {{ block.settings.arrow_background }};
    border: none;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }

  .ai-product-carousel__nav-{{ ai_gen_id }}:hover {
    background: {{ block.settings.arrow_hover_background }};
    transform: translateY(-50%) scale(1.1);
  }

  .ai-product-carousel__nav-{{ ai_gen_id }}:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: translateY(-50%) scale(1);
  }

  .ai-product-carousel__nav--prev-{{ ai_gen_id }} {
    left: -24px;
  }

  .ai-product-carousel__nav--next-{{ ai_gen_id }} {
    right: -24px;
  }

  .ai-product-carousel__nav-icon-{{ ai_gen_id }} {
    width: 20px;
    height: 20px;
    stroke: {{ block.settings.arrow_color }};
  }

  .ai-product-carousel__progress-{{ ai_gen_id }} {
    margin-top: 24px;
    height: 4px;
    background: {{ block.settings.progress_background }};
    border-radius: 2px;
    overflow: hidden;
  }

  .ai-product-carousel__progress-bar-{{ ai_gen_id }} {
    height: 100%;
    background: {{ block.settings.progress_color }};
    border-radius: 2px;
    transition: width 0.4s ease;
    width: 0%;
  }

  @media screen and (max-width: 1024px) {
    .ai-product-carousel__slide-{{ ai_gen_id }} {
      flex: 0 0 calc((100% - {{ block.settings.product_gap | times: 2 }}px) / 3);
    }
  }

  @media screen and (max-width: 768px) {
    .ai-product-carousel__slide-{{ ai_gen_id }} {
      flex: 0 0 calc((100% - {{ block.settings.product_gap }}px) / 2);
    }

    .ai-product-carousel__nav-{{ ai_gen_id }} {
      display: none;
    }

    .ai-product-carousel__container-{{ ai_gen_id }} {
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .ai-product-carousel__container-{{ ai_gen_id }}::-webkit-scrollbar {
      display: none;
    }

    .ai-product-carousel__slide-{{ ai_gen_id }} {
      scroll-snap-align: start;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-product-carousel__slide-{{ ai_gen_id }} {
      flex: 0 0 calc(100% - 20px);
    }
  }
{% endstyle %}

<product-carousel-{{ ai_gen_id }}
  class="ai-product-carousel-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% if block.settings.title != blank or block.settings.subtitle != blank %}
    <div class="ai-product-carousel__header-{{ ai_gen_id }}">
      {% if block.settings.title != blank %}
        <h2 class="ai-product-carousel__title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
      {% endif %}
      {% if block.settings.subtitle != blank %}
        <p class="ai-product-carousel__subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
      {% endif %}
    </div>
  {% endif %}

  <div class="ai-product-carousel__container-{{ ai_gen_id }}">
    <div class="ai-product-carousel__track-{{ ai_gen_id }}">
      {% if block.settings.product_collection != blank %}
        {% for product in block.settings.product_collection.products limit: 12 %}
          <div class="ai-product-carousel__slide-{{ ai_gen_id }}">
            <a href="{{ product.url }}" class="ai-product-carousel__link-{{ ai_gen_id }}">
              <div class="ai-product-carousel__image-container-{{ ai_gen_id }}">
                {% if product.featured_image %}
                  <img
                    src="{{ product.featured_image | image_url: width: 400 }}"
                    alt="{{ product.featured_image.alt | escape }}"
                    class="ai-product-carousel__image-{{ ai_gen_id }}"
                    loading="lazy"
                    width="400"
                    height="400"
                  >
                {% else %}
                  <div class="ai-product-carousel__image-placeholder-{{ ai_gen_id }}">
                    {{ 'product-1' | placeholder_svg_tag }}
                  </div>
                {% endif %}
              </div>
              <div class="ai-product-carousel__content-{{ ai_gen_id }}">
                <h3 class="ai-product-carousel__product-title-{{ ai_gen_id }}">{{ product.title }}</h3>
                <p class="ai-product-carousel__product-price-{{ ai_gen_id }}">{{ product.price | money }}</p>
              </div>
            </a>
          </div>
        {% endfor %}
      {% else %}
        {% for i in (1..8) %}
          <div class="ai-product-carousel__slide-{{ ai_gen_id }}">
            <div class="ai-product-carousel__image-container-{{ ai_gen_id }}">
              <div class="ai-product-carousel__image-placeholder-{{ ai_gen_id }}">
                {{ 'product-1' | placeholder_svg_tag }}
              </div>
            </div>
            <div class="ai-product-carousel__content-{{ ai_gen_id }}">
              <h3 class="ai-product-carousel__product-title-{{ ai_gen_id }}">Sample Product {{ i }}</h3>
              <p class="ai-product-carousel__product-price-{{ ai_gen_id }}">$29.99</p>
            </div>
          </div>
        {% endfor %}
      {% endif %}
    </div>

    <button
      class="ai-product-carousel__nav-{{ ai_gen_id }} ai-product-carousel__nav--prev-{{ ai_gen_id }}"
      aria-label="Previous products"
      disabled
    >
      <svg class="ai-product-carousel__nav-icon-{{ ai_gen_id }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>

    <button
      class="ai-product-carousel__nav-{{ ai_gen_id }} ai-product-carousel__nav--next-{{ ai_gen_id }}"
      aria-label="Next products"
    >
      <svg class="ai-product-carousel__nav-icon-{{ ai_gen_id }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>
  </div>

  <div class="ai-product-carousel__progress-{{ ai_gen_id }}">
    <div class="ai-product-carousel__progress-bar-{{ ai_gen_id }}"></div>
  </div>
</product-carousel-{{ ai_gen_id }}>

<script>
  (function() {
    class ProductCarousel{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.currentIndex = 0;
        this.slidesPerView = {{ block.settings.products_per_row }};
        this.slideGap = {{ block.settings.product_gap }};
      }

      connectedCallback() {
        this.track = this.querySelector('.ai-product-carousel__track-{{ ai_gen_id }}');
        this.slides = this.querySelectorAll('.ai-product-carousel__slide-{{ ai_gen_id }}');
        this.prevButton = this.querySelector('.ai-product-carousel__nav--prev-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-product-carousel__nav--next-{{ ai_gen_id }}');
        this.progressBar = this.querySelector('.ai-product-carousel__progress-bar-{{ ai_gen_id }}');
        this.container = this.querySelector('.ai-product-carousel__container-{{ ai_gen_id }}');

        this.totalSlides = this.slides.length;
        this.maxIndex = Math.max(0, this.totalSlides - this.slidesPerView);

        this.setupEventListeners();
        this.updateCarousel();
        this.handleResize();

        window.addEventListener('resize', () => this.handleResize());
      }

      setupEventListeners() {
        this.prevButton.addEventListener('click', () => this.goToPrevious());
        this.nextButton.addEventListener('click', () => this.goToNext());

        this.container.addEventListener('scroll', () => {
          if (window.innerWidth <= 768) {
            this.updateProgressFromScroll();
          }
        });

        this.track.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.track.addEventListener('touchmove', (e) => this.handleTouchMove(e));
      }

      handleResize() {
        if (window.innerWidth <= 480) {
          this.slidesPerView = 1;
        } else if (window.innerWidth <= 768) {
          this.slidesPerView = 2;
        } else if (window.innerWidth <= 1024) {
          this.slidesPerView = 3;
        } else {
          this.slidesPerView = {{ block.settings.products_per_row }};
        }

        this.maxIndex = Math.max(0, this.totalSlides - this.slidesPerView);
        this.currentIndex = Math.min(this.currentIndex, this.maxIndex);
        this.updateCarousel();
      }

      goToPrevious() {
        if (this.currentIndex > 0) {
          this.currentIndex--;
          this.updateCarousel();
        }
      }

      goToNext() {
        if (this.currentIndex < this.maxIndex) {
          this.currentIndex++;
          this.updateCarousel();
        }
      }

      updateCarousel() {
        if (window.innerWidth > 768) {
          const slideWidth = (100 / this.slidesPerView);
          const translateX = -(this.currentIndex * slideWidth);
          this.track.style.transform = `translateX(${translateX}%)`;
        }

        this.updateButtons();
        this.updateProgress();
      }

      updateButtons() {
        this.prevButton.disabled = this.currentIndex === 0;
        this.nextButton.disabled = this.currentIndex >= this.maxIndex;
      }

      updateProgress() {
        if (this.maxIndex === 0) {
          this.progressBar.style.width = '100%';
          return;
        }

        const progress = (this.currentIndex / this.maxIndex) * 100;
        this.progressBar.style.width = `${progress}%`;
      }

      updateProgressFromScroll() {
        const scrollLeft = this.container.scrollLeft;
        const maxScroll = this.container.scrollWidth - this.container.clientWidth;
        const progress = maxScroll > 0 ? (scrollLeft / maxScroll) * 100 : 0;
        this.progressBar.style.width = `${progress}%`;
      }

      handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
      }

      handleTouchMove(e) {
        if (!this.touchStartX) return;

        const touchEndX = e.touches[0].clientX;
        const diff = this.touchStartX - touchEndX;

        if (Math.abs(diff) > 50) {
          if (diff > 0 && this.currentIndex < this.maxIndex) {
            this.goToNext();
          } else if (diff < 0 && this.currentIndex > 0) {
            this.goToPrevious();
          }
          this.touchStartX = null;
        }
      }
    }

    customElements.define('product-carousel-{{ ai_gen_id }}', ProductCarousel{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Product Carousel",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Featured Products"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Discover our best-selling items"
    },
    {
      "type": "collection",
      "id": "product_collection",
      "label": "Product collection"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "min": 2,
      "max": 6,
      "step": 1,
      "label": "Products per row (desktop)",
      "default": 4
    },
    {
      "type": "range",
      "id": "product_gap",
      "min": 10,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Gap between products",
      "default": 20
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Container border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "card_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Product card border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 32
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "price_color",
      "label": "Price color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Product card background",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Navigation Arrows"
    },
    {
      "type": "color",
      "id": "arrow_background",
      "label": "Arrow background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "arrow_hover_background",
      "label": "Arrow hover background",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "arrow_color",
      "label": "Arrow color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Progress Bar"
    },
    {
      "type": "color",
      "id": "progress_background",
      "label": "Progress background",
      "default": "#e6e6e6"
    },
    {
      "type": "color",
      "id": "progress_color",
      "label": "Progress color",
      "default": "#000f9f"
    }
  ],
  "presets": [
    {
      "name": "Product Carousel"
    }
  ]
}
{% endschema %}
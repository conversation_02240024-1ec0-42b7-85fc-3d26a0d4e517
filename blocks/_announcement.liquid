

{% assign plain_text = block.settings.text | strip_newlines | strip_html | strip %}
{%- assign block_index = section.blocks | find_index: 'id', block.id -%}

{%- unless plain_text == '' -%}
  <slideshow-slide
    ref="slides[]"
    class="
      announcement-bar__slide
      text-block
      text-block--{{ block.id }}
      text-block--align-center
      text-block--full-width
      custom-typography
      {% if block.settings.font_size != "" %}custom-font-size{% endif %}
      {% if block.settings.weight != "" %}custom-font-weight{% endif %}
    "
    style="
      {% render 'typography-style', settings: block.settings, preset: 'custom' %}
      --width: 100%;
      --text-align: center;
      --line-height: 1;
    "
    {{ block.shopify_attributes }}
    aria-hidden="{% if block_index == 0 %}false{% else %}true{% endif %}"
  >
    <p class="announcement-bar__text">
      {{ block.settings.text }}
    </p>

    {% if block.settings.link != blank %}
      <a
        class="announcement-bar__link"
        href="{{ block.settings.link }}"
      >
        <span class="visually-hidden">
          {{ block.settings.text | strip_html }}
        </span>
      </a>
    {% endif %}
  </slideshow-slide>
{%- endunless -%}

{% stylesheet %}
  .text-block {
    width: var(--width);
    max-width: 100%;
  }

  .text-block > * {
    width: var(--width);
    max-width: var(--max-width, 100%);
    text-align: var(--text-align);
  }

  .text-block:not(.text-block--full-width).rte,
  .text-block:not(.text-block--full-width).paragraph {
    /* Safari doesn't support pretty, so fallback to balance */
    text-wrap: balance;
    text-wrap: pretty;
  }

  .text-block:not(.text-block--full-width).h1,
  .text-block:not(.text-block--full-width).h2,
  .text-block:not(.text-block--full-width).h3,
  .text-block:not(.text-block--full-width).h4,
  .text-block:not(.text-block--full-width).h5,
  .text-block:not(.text-block--full-width).h6 {
    text-wrap: balance;
  }

  /* Hide underline unless text is using paragraph styles. */
  .text-block:is(.h1, .h2, .h3, .h4, .h5, .h6) a {
    text-decoration-color: transparent;
  }

  .text-block h1,
  .text-block.h1 > * {
    margin-block: var(--font-h1--spacing);
  }

  .text-block h2,
  .text-block.h2 > * {
    margin-block: var(--font-h2--spacing);
  }

  .text-block h3,
  .text-block.h3 > * {
    margin-block: var(--font-h3--spacing);
  }

  .text-block h4,
  .text-block.h4 > * {
    margin-block: var(--font-h4--spacing);
  }

  .text-block h5,
  .text-block.h5 > * {
    margin-block: var(--font-h5--spacing);
  }

  .text-block h6,
  .text-block.h6 > * {
    margin-block: var(--font-h6--spacing);
  }

  .text-block > *:first-child {
    margin-block-start: 0;
  }

  .text-block > *:last-child {
    margin-block-end: 0;
  }

  .text-block--align-center,
  .text-block--align-center > * {
    margin-inline: auto;
  }

  .text-block--align-right,
  .text-block--align-right > * {
    margin-inline-start: auto;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.announcement",
  "tag": null,
  "settings": [
    {
      "type": "inline_richtext",
      "id": "text",
      "label": "t:settings.text",
      "default": "t:text_defaults.shop_our_latest_arrivals"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    },
    {
      "type": "header",
      "content": "t:content.typography"
    },
    {
      "type": "select",
      "id": "font",
      "label": "t:settings.font",
      "options": [
        {
          "value": "var(--font-body--family)",
          "label": "t:options.body"
        },
        {
          "value": "var(--font-subheading--family)",
          "label": "t:options.subheading"
        },
        {
          "value": "var(--font-heading--family)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--font-accent--family)",
          "label": "t:options.accent"
        }
      ],
      "default": "var(--font-body--family)"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        },
        {
          "value": "1.25rem",
          "label": "20px"
        },
        {
          "value": "1.5rem",
          "label": "24px"
        },
        {
          "value": "2rem",
          "label": "32px"
        },
        {
          "value": "2.5rem",
          "label": "40px"
        },
        {
          "value": "3rem",
          "label": "48px"
        },
        {
          "value": "3.5rem",
          "label": "56px"
        },
        {
          "value": "4.5rem",
          "label": "72px"
        },
        {
          "value": "5.5rem",
          "label": "88px"
        },
        {
          "value": "7.5rem",
          "label": "120px"
        },
        {
          "value": "9.5rem",
          "label": "152px"
        },
        {
          "value": "11.5rem",
          "label": "184px"
        }
      ],
      "default": "1rem"
    },
    {
      "type": "select",
      "id": "weight",
      "label": "t:settings.weight",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "100",
          "label": "t:options.thin"
        },
        {
          "value": "300",
          "label": "t:options.light"
        },
        {
          "value": "400",
          "label": "t:options.regular"
        },
        {
          "value": "500",
          "label": "t:options.medium"
        },
        {
          "value": "600",
          "label": "t:options.semibold"
        },
        {
          "value": "700",
          "label": "t:options.bold"
        },
        {
          "value": "900",
          "label": "t:options.black"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "letter_spacing",
      "label": "t:settings.letter_spacing",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    }
  ],
  "presets": [
    {
      "name": "t:names.announcement"
    }
  ]
}
{% endschema %}



{% capture children %}
  {% content_for 'blocks' %}
{% endcapture %}

{% render 'group',
  class: 'media-with-content__content',
  children: children,
  settings: block.settings,
  shopify_attributes: block.shopify_attributes
%}

{% schema %}
{
  "name": "t:names.content",
  "tag": null,
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "button"
    },
    {
      "type": "video"
    },
    {
      "type": "group"
    },
    {
      "type": "spacer"
    },
    {
      "type": "_divider"
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "horizontal_alignment_flex_direction_column",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "vertical_alignment_flex_direction_column",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    }
  ],
  "presets": [
    {
      "name": "t:names.group",
      "category": "t:categories.layout"
    }
  ]
}
{% endschema %}

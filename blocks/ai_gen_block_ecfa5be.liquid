{% doc %}
  @prompt
    Create a video slider section that displays 4 videos at a time in a horizontal carousel layout. The slider should support RTL (right-to-left) direction and allow merchants to add up to 10 videos total. Include navigation controls, responsive design that adapts the number of visible videos on smaller screens, and settings for merchants to upload videos, add titles, and customize the slider appearance. The slider should have smooth transitions and proper RTL text alignment., Add video title and description for each video, hide video controls, show play button only, on click of arrow show next/previous video 
    

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-slider-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    padding: {{ block.settings.section_padding }}px 0;
    background-color: {{ block.settings.background_color }};
    direction: {% if block.settings.rtl_direction %}rtl{% else %}ltr{% endif %};
  }

  .ai-video-slider-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .ai-video-slider-header-{{ ai_gen_id }} {
    text-align: {% if block.settings.rtl_direction %}right{% else %}left{% endif %};
    margin-bottom: 30px;
  }

  .ai-video-slider-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    margin: 0 0 10px;
  }

  .ai-video-slider-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.description_color }};
    margin: 0;
  }

  .ai-video-slider-wrapper-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.slider_border_radius }}px;
  }

  .ai-video-slider-track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.5s ease;
    gap: {{ block.settings.video_gap }}px;
  }

  .ai-video-slider-slide-{{ ai_gen_id }} {
    flex: 0 0 calc(25% - {{ block.settings.video_gap | times: 0.75 }}px);
    position: relative;
    border-radius: {{ block.settings.video_border_radius }}px;
    overflow: hidden;
    background-color: #000;
  }

  .ai-video-slider-video-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.video_height }}px;
    overflow: hidden;
    cursor: pointer;
  }

  .ai-video-slider-video-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .ai-video-slider-play-button-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: {{ block.settings.play_button_color }};
    color: {{ block.settings.play_button_icon_color }};
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 2;
  }

  .ai-video-slider-play-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.play_button_hover_color }};
    transform: translate(-50%, -50%) scale(1.1);
  }

  .ai-video-slider-play-button-{{ ai_gen_id }}.playing {
    opacity: 0;
    pointer-events: none;
  }

  .ai-video-slider-play-button-{{ ai_gen_id }} svg {
    width: 24px;
    height: 24px;
    margin-left: 3px;
  }

  .ai-video-slider-video-overlay-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 30px 15px 15px;
    text-align: {% if block.settings.rtl_direction %}right{% else %}left{% endif %};
  }

  .ai-video-slider-video-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.video_title_size }}px;
    font-weight: 600;
    margin: 0 0 8px;
    line-height: 1.3;
  }

  .ai-video-slider-video-desc-{{ ai_gen_id }} {
    font-size: {{ block.settings.video_desc_size }}px;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }

  .ai-video-slider-nav-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: {{ block.settings.nav_button_color }};
    color: {{ block.settings.nav_button_text_color }};
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 3;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .ai-video-slider-nav-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.nav_button_hover_color }};
    transform: translateY(-50%) scale(1.1);
  }

  .ai-video-slider-nav-{{ ai_gen_id }}:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%) scale(1);
  }

  .ai-video-slider-nav-prev-{{ ai_gen_id }} {
    {% if block.settings.rtl_direction %}
      right: -25px;
    {% else %}
      left: -25px;
    {% endif %}
  }

  .ai-video-slider-nav-next-{{ ai_gen_id }} {
    {% if block.settings.rtl_direction %}
      left: -25px;
    {% else %}
      right: -25px;
    {% endif %}
  }

  .ai-video-slider-nav-{{ ai_gen_id }} svg {
    width: 20px;
    height: 20px;
    {% if block.settings.rtl_direction %}
      transform: scaleX(-1);
    {% endif %}
  }

  .ai-video-slider-dots-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
  }

  .ai-video-slider-dot-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: {{ block.settings.dot_color }};
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.5;
  }

  .ai-video-slider-dot-{{ ai_gen_id }}.active {
    background-color: {{ block.settings.dot_active_color }};
    opacity: 1;
    transform: scale(1.2);
  }

  @media screen and (max-width: 990px) {
    .ai-video-slider-slide-{{ ai_gen_id }} {
      flex: 0 0 calc(33.333% - {{ block.settings.video_gap | times: 0.667 }}px);
    }
  }

  @media screen and (max-width: 749px) {
    .ai-video-slider-slide-{{ ai_gen_id }} {
      flex: 0 0 calc(50% - {{ block.settings.video_gap | times: 0.5 }}px);
    }

    .ai-video-slider-nav-{{ ai_gen_id }} {
      width: 40px;
      height: 40px;
    }

    .ai-video-slider-nav-{{ ai_gen_id }} svg {
      width: 16px;
      height: 16px;
    }

    .ai-video-slider-nav-prev-{{ ai_gen_id }} {
      {% if block.settings.rtl_direction %}
        right: -20px;
      {% else %}
        left: -20px;
      {% endif %}
    }

    .ai-video-slider-nav-next-{{ ai_gen_id }} {
      {% if block.settings.rtl_direction %}
        left: -20px;
      {% else %}
        right: -20px;
      {% endif %}
    }

    .ai-video-slider-video-container-{{ ai_gen_id }} {
      height: {{ block.settings.video_height | times: 0.8 }}px;
    }

    .ai-video-slider-play-button-{{ ai_gen_id }} {
      width: 50px;
      height: 50px;
    }

    .ai-video-slider-play-button-{{ ai_gen_id }} svg {
      width: 20px;
      height: 20px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-video-slider-slide-{{ ai_gen_id }} {
      flex: 0 0 calc(100% - {{ block.settings.video_gap }}px);
    }
  }
{% endstyle %}

<video-slider-{{ ai_gen_id }}
  class="ai-video-slider-{{ ai_gen_id }}"
  data-rtl="{{ block.settings.rtl_direction }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-video-slider-container-{{ ai_gen_id }}">
    {% if block.settings.section_title != blank or block.settings.section_description != blank %}
      <div class="ai-video-slider-header-{{ ai_gen_id }}">
        {% if block.settings.section_title != blank %}
          <h2 class="ai-video-slider-title-{{ ai_gen_id }}">{{ block.settings.section_title }}</h2>
        {% endif %}
        {% if block.settings.section_description != blank %}
          <p class="ai-video-slider-description-{{ ai_gen_id }}">{{ block.settings.section_description }}</p>
        {% endif %}
      </div>
    {% endif %}

    <div class="ai-video-slider-wrapper-{{ ai_gen_id }}">
      <div class="ai-video-slider-track-{{ ai_gen_id }}">
        {% for i in (1..10) %}
          {% liquid
            assign video_key = 'video_' | append: i
            assign title_key = 'video_title_' | append: i
            assign desc_key = 'video_description_' | append: i
            assign video = block.settings[video_key]
            assign title = block.settings[title_key]
            assign description = block.settings[desc_key]
          %}

          {% if video != blank %}
            <div class="ai-video-slider-slide-{{ ai_gen_id }}">
              <div class="ai-video-slider-video-container-{{ ai_gen_id }}" data-video-index="{{ forloop.index0 }}">
                <video
                  class="ai-video-slider-video-{{ ai_gen_id }}"
                  preload="metadata"
                  playsinline
                  muted
                >
                  <source src="{{ video }}" type="video/mp4">
                  Your browser does not support the video tag.
                </video>
                
                <button class="ai-video-slider-play-button-{{ ai_gen_id }}" aria-label="Play video">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                </button>

                {% if title != blank or description != blank %}
                  <div class="ai-video-slider-video-overlay-{{ ai_gen_id }}">
                    {% if title != blank %}
                      <h3 class="ai-video-slider-video-title-{{ ai_gen_id }}">{{ title }}</h3>
                    {% endif %}
                    {% if description != blank %}
                      <p class="ai-video-slider-video-desc-{{ ai_gen_id }}">{{ description }}</p>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <button
        class="ai-video-slider-nav-{{ ai_gen_id }} ai-video-slider-nav-prev-{{ ai_gen_id }}"
        aria-label="Previous videos"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>

      <button
        class="ai-video-slider-nav-{{ ai_gen_id }} ai-video-slider-nav-next-{{ ai_gen_id }}"
        aria-label="Next videos"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    </div>

    <div class="ai-video-slider-dots-{{ ai_gen_id }}"></div>
  </div>
</video-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class VideoSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.currentSlide = 0;
        this.slidesPerView = 4;
        this.isRTL = this.dataset.rtl === 'true';
        this.currentPlayingVideo = null;
      }

      connectedCallback() {
        this.track = this.querySelector('.ai-video-slider-track-{{ ai_gen_id }}');
        this.slides = this.querySelectorAll('.ai-video-slider-slide-{{ ai_gen_id }}');
        this.prevBtn = this.querySelector('.ai-video-slider-nav-prev-{{ ai_gen_id }}');
        this.nextBtn = this.querySelector('.ai-video-slider-nav-next-{{ ai_gen_id }}');
        this.dotsContainer = this.querySelector('.ai-video-slider-dots-{{ ai_gen_id }}');

        if (this.slides.length === 0) return;

        this.updateSlidesPerView();
        this.createDots();
        this.updateSlider();
        this.setupEventListeners();

        window.addEventListener('resize', () => {
          this.updateSlidesPerView();
          this.createDots();
          this.updateSlider();
        });
      }

      updateSlidesPerView() {
        const width = window.innerWidth;
        if (width <= 480) {
          this.slidesPerView = 1;
        } else if (width <= 749) {
          this.slidesPerView = 2;
        } else if (width <= 990) {
          this.slidesPerView = 3;
        } else {
          this.slidesPerView = 4;
        }
      }

      createDots() {
        this.dotsContainer.innerHTML = '';
        const totalPages = Math.ceil(this.slides.length / this.slidesPerView);

        for (let i = 0; i < totalPages; i++) {
          const dot = document.createElement('button');
          dot.className = `ai-video-slider-dot-{{ ai_gen_id }}`;
          dot.setAttribute('aria-label', `Go to slide group ${i + 1}`);
          dot.addEventListener('click', () => this.goToSlide(i));
          this.dotsContainer.appendChild(dot);
        }
      }

      setupEventListeners() {
        this.prevBtn.addEventListener('click', () => this.previousSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());

        this.slides.forEach(slide => {
          const videoContainer = slide.querySelector('.ai-video-slider-video-container-{{ ai_gen_id }}');
          const video = slide.querySelector('.ai-video-slider-video-{{ ai_gen_id }}');
          const playButton = slide.querySelector('.ai-video-slider-play-button-{{ ai_gen_id }}');

          if (video && playButton) {
            playButton.addEventListener('click', (e) => {
              e.stopPropagation();
              this.toggleVideo(video, playButton);
            });

            videoContainer.addEventListener('click', () => {
              if (!video.paused) {
                this.pauseVideo(video, playButton);
              }
            });

            video.addEventListener('ended', () => {
              this.pauseVideo(video, playButton);
            });

            video.addEventListener('pause', () => {
              playButton.classList.remove('playing');
              if (this.currentPlayingVideo === video) {
                this.currentPlayingVideo = null;
              }
            });

            video.addEventListener('play', () => {
              playButton.classList.add('playing');
              if (this.currentPlayingVideo && this.currentPlayingVideo !== video) {
                const currentPlayButton = this.currentPlayingVideo.parentElement.querySelector('.ai-video-slider-play-button-{{ ai_gen_id }}');
                this.pauseVideo(this.currentPlayingVideo, currentPlayButton);
              }
              this.currentPlayingVideo = video;
            });
          }
        });
      }

      toggleVideo(video, playButton) {
        if (video.paused) {
          this.playVideo(video, playButton);
        } else {
          this.pauseVideo(video, playButton);
        }
      }

      playVideo(video, playButton) {
        video.play();
      }

      pauseVideo(video, playButton) {
        video.pause();
      }

      updateSlider() {
        const totalPages = Math.ceil(this.slides.length / this.slidesPerView);
        const maxSlide = totalPages - 1;

        if (this.currentSlide > maxSlide) {
          this.currentSlide = maxSlide;
        }

        const translateValue = this.currentSlide * 100;
        const direction = this.isRTL ? '' : '-';
        this.track.style.transform = `translateX(${direction}${translateValue}%)`;

        this.prevBtn.disabled = this.currentSlide === 0;
        this.nextBtn.disabled = this.currentSlide >= maxSlide;

        const dots = this.querySelectorAll('.ai-video-slider-dot-{{ ai_gen_id }}');
        dots.forEach((dot, index) => {
          dot.classList.toggle('active', index === this.currentSlide);
        });

        if (this.currentPlayingVideo) {
          const currentPlayButton = this.currentPlayingVideo.parentElement.querySelector('.ai-video-slider-play-button-{{ ai_gen_id }}');
          this.pauseVideo(this.currentPlayingVideo, currentPlayButton);
        }
      }

      goToSlide(slideIndex) {
        this.currentSlide = slideIndex;
        this.updateSlider();
      }

      nextSlide() {
        const totalPages = Math.ceil(this.slides.length / this.slidesPerView);
        if (this.currentSlide < totalPages - 1) {
          this.currentSlide++;
          this.updateSlider();
        }
      }

      previousSlide() {
        if (this.currentSlide > 0) {
          this.currentSlide--;
          this.updateSlider();
        }
      }
    }

    customElements.define('video-slider-{{ ai_gen_id }}', VideoSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Video Slider",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "section_title",
      "label": "Section title",
      "default": "Featured Videos"
    },
    {
      "type": "textarea",
      "id": "section_description",
      "label": "Section description",
      "default": "Discover our latest video content"
    },
    {
      "type": "checkbox",
      "id": "rtl_direction",
      "label": "Enable RTL direction",
      "default": false
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Section padding",
      "default": 50
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 32
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "range",
      "id": "slider_border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Slider border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "video_gap",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Video gap",
      "default": 16
    },
    {
      "type": "range",
      "id": "video_height",
      "min": 200,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Video height",
      "default": 280
    },
    {
      "type": "range",
      "id": "video_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Video border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "video_title_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Video title size",
      "default": 16
    },
    {
      "type": "range",
      "id": "video_desc_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Video description size",
      "default": 12
    },
    {
      "type": "header",
      "content": "Play Button Settings"
    },
    {
      "type": "color",
      "id": "play_button_color",
      "label": "Play button color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "play_button_icon_color",
      "label": "Play button icon color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "play_button_hover_color",
      "label": "Play button hover color",
      "default": "#f0f0f0"
    },
    {
      "type": "header",
      "content": "Navigation Settings"
    },
    {
      "type": "color",
      "id": "nav_button_color",
      "label": "Navigation button color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "nav_button_text_color",
      "label": "Navigation button text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "nav_button_hover_color",
      "label": "Navigation button hover color",
      "default": "#f0f0f0"
    },
    {
      "type": "color",
      "id": "dot_color",
      "label": "Dot color",
      "default": "#cccccc"
    },
    {
      "type": "color",
      "id": "dot_active_color",
      "label": "Active dot color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Video 1"
    },
    {
      "type": "video",
      "id": "video_1",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_1",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_1",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 2"
    },
    {
      "type": "video",
      "id": "video_2",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_2",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_2",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 3"
    },
    {
      "type": "video",
      "id": "video_3",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_3",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_3",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 4"
    },
    {
      "type": "video",
      "id": "video_4",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_4",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_4",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 5"
    },
    {
      "type": "video",
      "id": "video_5",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_5",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_5",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 6"
    },
    {
      "type": "video",
      "id": "video_6",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_6",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_6",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 7"
    },
    {
      "type": "video",
      "id": "video_7",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_7",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_7",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 8"
    },
    {
      "type": "video",
      "id": "video_8",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_8",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_8",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 9"
    },
    {
      "type": "video",
      "id": "video_9",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_9",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_9",
      "label": "Video description"
    },
    {
      "type": "header",
      "content": "Video 10"
    },
    {
      "type": "video",
      "id": "video_10",
      "label": "Video file"
    },
    {
      "type": "text",
      "id": "video_title_10",
      "label": "Video title"
    },
    {
      "type": "textarea",
      "id": "video_description_10",
      "label": "Video description"
    }
  ],
  "presets": [
    {
      "name": "Video Slider"
    }
  ]
}
{% endschema %}
{% doc %}
  @prompt
    I want to add share button option with share icon in product page functional, I just want to display one share icon and on click it shows social icons in the toiltip  or small popup, also make the option that I can put the custom share icon

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-share-dropdown-{{ ai_gen_id }} {
    position: relative;
    display: inline-block;
     width: 100%;
  }

  .ai-share-trigger-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0px;
    bottom: 25px;
    width: {{ block.settings.trigger_size }}px;
    {% comment %} height: {{ block.settings.trigger_size }}px; {% endcomment %}
    background-color: {{ block.settings.trigger_bg_color }};
    color: {{ block.settings.trigger_text_color }};
    border: {{ block.settings.trigger_border_width }}px solid {{ block.settings.trigger_border_color }};
    border-radius: {{ block.settings.trigger_border_radius }}px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0;
    overflow: hidden;
  }

  .ai-share-trigger-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.trigger_hover_bg_color }};
    color: {{ block.settings.trigger_hover_text_color }};
    transform: translateY(-1px);
  }

  .ai-share-trigger-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.trigger_size }}px * 0.5);
    height: calc({{ block.settings.trigger_size }}px * 0.5);
    fill: currentColor;
  }

  .ai-share-trigger-{{ ai_gen_id }} img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-share-trigger-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-share-trigger-placeholder-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.trigger_size }}px * 0.5);
    height: calc({{ block.settings.trigger_size }}px * 0.5);
    fill: #999;
  }

  .ai-share-popup-{{ ai_gen_id }} {
    position: absolute;
    top: calc(100% + 8px);
    left: 15%;
    transform: translateX(-50%);
    background-color: {{ block.settings.popup_bg_color }};
    border-radius: {{ block.settings.popup_border_radius }}px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: {{ block.settings.popup_padding }}px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s ease;
    min-width: 200px;
  }

  .ai-share-popup-{{ ai_gen_id }}.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }

  .ai-share-popup-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid {{ block.settings.popup_bg_color }};
  }

  .ai-share-popup__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.popup_title_size }}px;
    color: {{ block.settings.popup_text_color }};
    margin: 0 0 12px 0;
    font-weight: 600;
    text-align: center;
  }

  .ai-share-popup__buttons-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.button_spacing }}px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .ai-share-button-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    width: {{ block.settings.button_size }}px;
    height: {{ block.settings.button_size }}px;
    border-radius: {{ block.settings.button_border_radius }}px;
    text-decoration: none;
    transition: transform 0.2s ease, opacity 0.2s ease;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .ai-share-button-{{ ai_gen_id }}:hover {
    transform: translateY(-2px);
    opacity: 0.9;
  }

  .ai-share-button-{{ ai_gen_id }} svg {
    width: calc({{ block.settings.button_size }}px * 0.5);
    height: calc({{ block.settings.button_size }}px * 0.5);
    fill: currentColor;
  }

  .ai-share-button--facebook-{{ ai_gen_id }} {
    background-color: #1877f2;
    color: white;
  }

  .ai-share-button--twitter-{{ ai_gen_id }} {
    background-color: #000;
    color: white;
  }
  .ai-share-button--x-{{ ai_gen_id }} {
  background-color: #000;
  padding: 8px;
  border: none;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ai-share-button--x-{{ ai_gen_id }} svg {
  fill: #fff;
  width: 20px;
  height: 20px;
}


  .ai-share-button--pinterest-{{ ai_gen_id }} {
    background-color: #bd081c;
    color: white;
  }

  .ai-share-button--email-{{ ai_gen_id }} {
    background-color: #34495e;
    color: white;
  }

  .ai-share-button--copy-{{ ai_gen_id }} {
    background-color: {{ block.settings.copy_button_color }};
    color: {{ block.settings.copy_button_text_color }};
  }

  .ai-share-button--copy-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.copy_button_hover_color }};
  }

  .ai-share-notification-{{ ai_gen_id }} {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: {{ block.settings.notification_bg_color }};
    color: {{ block.settings.notification_text_color }};
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .ai-share-notification-{{ ai_gen_id }}.show {
    opacity: 1;
    transform: translateX(0);
  }

  @media screen and (max-width: 749px) {
    .ai-share-popup-{{ ai_gen_id }} {
      left: 0;
      right: 0;
      transform: translateY(-10px);
      margin: 0 10px;
      min-width: auto;
    }

    .ai-share-popup-{{ ai_gen_id }}.active {
      transform: translateY(0);
    }

    .ai-share-popup-{{ ai_gen_id }}::before {
      left: {{ block.settings.trigger_size | divided_by: 2 }}px;
      transform: none;
    }

    .ai-share-notification-{{ ai_gen_id }} {
      right: 10px;
      left: 10px;
    }
  }
{% endstyle %}

<product-share-dropdown-{{ ai_gen_id }}
  class="ai-share-dropdown-{{ ai_gen_id }}"
  data-product-url="{{ shop.url }}{{ product.url }}"
  data-product-title="{{ product.title | escape }}"
  data-product-image="{% if product.featured_image %}{{ product.featured_image | image_url: width: 1200 }}{% endif %}"
  {{ block.shopify_attributes }}
>
  <button
    class="ai-share-trigger-{{ ai_gen_id }}"
    aria-label="Share this product"
    aria-expanded="false"
    aria-haspopup="true"
  >
    {% if block.settings.custom_share_icon %}
      <img
        src="{{ block.settings.custom_share_icon | image_url: width: 100 }}"
        alt="Share icon"
        width="100"
        height="100"
        loading="lazy"
      >
    {% else %}
      {% if block.settings.icon_style == 'share' %}
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
        </svg>
      {% elsif block.settings.icon_style == 'export' %}
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          <path d="M12 2l3.09 3.09L9 11.17l1.41 1.41 6.09-6.09L20 10V2z"/>
        </svg>
      {% elsif block.settings.icon_style == 'link' %}
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
        </svg>
      {% else %}
        <div class="ai-share-trigger-placeholder-{{ ai_gen_id }}">
          {{ 'image' | placeholder_svg_tag }}
        </div>
      {% endif %}
    {% endif %}
  </button>

  <div class="ai-share-popup-{{ ai_gen_id }}" id="ai-share-popup-{{ ai_gen_id }}">
    {% if block.settings.popup_title != blank %}
      <h3 class="ai-share-popup__title-{{ ai_gen_id }}">{{ block.settings.popup_title }}</h3>
    {% endif %}

    <div class="ai-share-popup__buttons-{{ ai_gen_id }}">
      {% if block.settings.show_facebook %}
        <button
          class="ai-share-button-{{ ai_gen_id }} ai-share-button--facebook-{{ ai_gen_id }}"
          data-share="facebook"
          aria-label="Share on Facebook"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
        </button>
      {% endif %}

      {% if block.settings.show_twitter %}
  <button
    class="ai-share-button-{{ ai_gen_id }} ai-share-button--x-{{ ai_gen_id }}"
    data-share="twitter"
    aria-label="Share on X"
  >
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="#fff">
      <path d="M14.743 10.451L23.24 0h-2.104l-7.57 8.933L8.52 0H0l9.008 13.157L0 24h2.104l8.019-9.461L15.48 24H24l-9.257-13.549zm-2.05 2.421l-1.026-1.503L3.03 1.56h4.137l5.373 7.873 1.025 1.503 8.703 12.763h-4.137l-5.398-7.828z"/>
    </svg>
  </button>
{% endif %}

      {% if block.settings.show_pinterest %}
        <button
          class="ai-share-button-{{ ai_gen_id }} ai-share-button--pinterest-{{ ai_gen_id }}"
          data-share="pinterest"
          aria-label="Share on Pinterest"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.748-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
          </svg>
        </button>
      {% endif %}

      {% if block.settings.show_email %}
        <button
          class="ai-share-button-{{ ai_gen_id }} ai-share-button--email-{{ ai_gen_id }}"
          data-share="email"
          aria-label="Share via Email"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
          </svg>
        </button>
      {% endif %}

      {% if block.settings.show_copy_link %}
        <button
          class="ai-share-button-{{ ai_gen_id }} ai-share-button--copy-{{ ai_gen_id }}"
          data-share="copy"
          aria-label="Copy Link"
        >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
      {% endif %}
    </div>
  </div>

  <div class="ai-share-notification-{{ ai_gen_id }}" id="ai-share-notification-{{ ai_gen_id }}">
    {{ block.settings.copy_success_message }}
  </div>
</product-share-dropdown-{{ ai_gen_id }}>

<script>
  (function() {
    class ProductShareDropdown{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.productUrl = this.dataset.productUrl;
        this.productTitle = this.dataset.productTitle;
        this.productImage = this.dataset.productImage;
        this.isOpen = false;
      }

      connectedCallback() {
        this.setupEventListeners();
      }

      setupEventListeners() {
        const trigger = this.querySelector('.ai-share-trigger-{{ ai_gen_id }}');
        const popup = this.querySelector('.ai-share-popup-{{ ai_gen_id }}');
        const shareButtons = this.querySelectorAll('[data-share]');

        trigger.addEventListener('click', (e) => {
          e.stopPropagation();
          this.togglePopup();
        });

        shareButtons.forEach(button => {
          if (button !== trigger) {
            button.addEventListener('click', (e) => {
              e.preventDefault();
              const shareType = button.dataset.share;
              this.handleShare(shareType);
              this.closePopup();
            });
          }
        });

        document.addEventListener('click', (e) => {
          if (!this.contains(e.target)) {
            this.closePopup();
          }
        });

        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && this.isOpen) {
            this.closePopup();
          }
        });
      }

      togglePopup() {
        if (this.isOpen) {
          this.closePopup();
        } else {
          this.openPopup();
        }
      }

      openPopup() {
        const trigger = this.querySelector('.ai-share-trigger-{{ ai_gen_id }}');
        const popup = this.querySelector('.ai-share-popup-{{ ai_gen_id }}');
        
        popup.classList.add('active');
        trigger.setAttribute('aria-expanded', 'true');
        this.isOpen = true;
      }

      closePopup() {
        const trigger = this.querySelector('.ai-share-trigger-{{ ai_gen_id }}');
        const popup = this.querySelector('.ai-share-popup-{{ ai_gen_id }}');
        
        popup.classList.remove('active');
        trigger.setAttribute('aria-expanded', 'false');
        this.isOpen = false;
      }

      handleShare(type) {
        const url = encodeURIComponent(this.productUrl);
        const title = encodeURIComponent(this.productTitle);
        const image = encodeURIComponent(this.productImage);
        
        let shareUrl = '';
        
        switch(type) {
          case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            break;
          case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
            break;
          case 'pinterest':
            shareUrl = `https://pinterest.com/pin/create/button/?url=${url}&media=${image}&description=${title}`;
            break;
          case 'email':
            shareUrl = `mailto:?subject=${title}&body=Check out this product: ${url}`;
            break;
          case 'copy':
            this.copyToClipboard();
            return;
        }
        
        if (shareUrl) {
          window.open(shareUrl, '_blank', 'width=600,height=400');
        }
      }

      async copyToClipboard() {
        try {
          await navigator.clipboard.writeText(this.productUrl);
          this.showNotification();
        } catch (err) {
          const textArea = document.createElement('textarea');
          textArea.value = this.productUrl;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.showNotification();
        }
      }

      showNotification() {
        const notification = this.querySelector('.ai-share-notification-{{ ai_gen_id }}');
        notification.classList.add('show');
        
        setTimeout(() => {
          notification.classList.remove('show');
        }, 3000);
      }
    }

    customElements.define('product-share-dropdown-{{ ai_gen_id }}', ProductShareDropdown{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Product Share Dropdown",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "popup_title",
      "label": "Popup title",
      "default": "Share this product"
    },
    {
      "type": "text",
      "id": "copy_success_message",
      "label": "Copy success message",
      "default": "Link copied to clipboard!"
    },
    {
      "type": "header",
      "content": "Trigger Icon"
    },
    {
      "type": "image_picker",
      "id": "custom_share_icon",
      "label": "Custom share icon"
    },
    {
      "type": "select",
      "id": "icon_style",
      "label": "Default icon style",
      "info": "Used when no custom icon is uploaded",
      "options": [
        {
          "value": "share",
          "label": "Share"
        },
        {
          "value": "export",
          "label": "Export"
        },
        {
          "value": "link",
          "label": "Link"
        }
      ],
      "default": "share"
    },
    {
      "type": "header",
      "content": "Social Platforms"
    },
    {
      "type": "checkbox",
      "id": "show_facebook",
      "label": "Show Facebook",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_twitter",
      "label": "Show X",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_pinterest",
      "label": "Show Pinterest",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_email",
      "label": "Show Email",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_copy_link",
      "label": "Show Copy Link",
      "default": true
    },
    {
      "type": "header",
      "content": "Trigger Button Style"
    },
    {
      "type": "range",
      "id": "trigger_size",
      "min": 30,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Trigger button size",
      "default": 44
    },
    {
      "type": "range",
      "id": "trigger_border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Trigger border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "trigger_border_width",
      "min": 0,
      "max": 4,
      "step": 1,
      "unit": "px",
      "label": "Trigger border width",
      "default": 1
    },
    {
      "type": "color",
      "id": "trigger_bg_color",
      "label": "Trigger background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "trigger_text_color",
      "label": "Trigger icon color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "trigger_border_color",
      "label": "Trigger border color",
      "default": "#e6e6e6"
    },
    {
      "type": "color",
      "id": "trigger_hover_bg_color",
      "label": "Trigger hover background",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "trigger_hover_text_color",
      "label": "Trigger hover icon color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Popup Style"
    },
    {
      "type": "range",
      "id": "popup_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Popup border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "popup_padding",
      "min": 8,
      "max": 24,
      "step": 2,
      "unit": "px",
      "label": "Popup padding",
      "default": 16
    },
    {
      "type": "range",
      "id": "popup_title_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Popup title size",
      "default": 14
    },
    {
      "type": "color",
      "id": "popup_bg_color",
      "label": "Popup background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "popup_text_color",
      "label": "Popup text color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Share Buttons Style"
    },
    {
      "type": "range",
      "id": "button_size",
      "min": 24,
      "max": 44,
      "step": 2,
      "unit": "px",
      "label": "Share button size",
      "default": 36
    },
    {
      "type": "range",
      "id": "button_spacing",
      "min": 4,
      "max": 16,
      "step": 2,
      "unit": "px",
      "label": "Button spacing",
      "default": 8
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Share button border radius",
      "default": 6
    },
    {
      "type": "color",
      "id": "copy_button_color",
      "label": "Copy button color",
      "default": "#6c757d"
    },
    {
      "type": "color",
      "id": "copy_button_text_color",
      "label": "Copy button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "copy_button_hover_color",
      "label": "Copy button hover color",
      "default": "#5a6268"
    },
    {
      "type": "header",
      "content": "Notification"
    },
    {
      "type": "color",
      "id": "notification_bg_color",
      "label": "Notification background",
      "default": "#28a745"
    },
    {
      "type": "color",
      "id": "notification_text_color",
      "label": "Notification text color",
      "default": "#ffffff"
    }
  ],
  "presets": [
    {
      "name": "Product Share Dropdown"
    }
  ]
}
{% endschema %}
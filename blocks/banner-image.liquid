
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-banner-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

  .ai-banner-link-{{ ai_gen_id }} {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
  }

  .ai-banner-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: auto;
  }

  .ai-banner-image-{{ ai_gen_id }} {
    width: 100%;
    height: auto;
    display: block;
  }

  .ai-banner-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 400px;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ai-banner-placeholder-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    max-width: 300px;
    max-height: 200px;
    opacity: 0.3;
  }

  .ai-banner-empty-state-{{ ai_gen_id }} {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    text-align: center;pointer-events: none;
  }

  .ai-banner-mobile-image-{{ ai_gen_id }} {
    display: none;
  }

  @media screen and (max-width: 749px) {
    .ai-banner-desktop-image-{{ ai_gen_id }} {
      display: none;
    }

    .ai-banner-mobile-image-{{ ai_gen_id }} {
      display: block;
    }
  }
{% endstyle %}

<div class="ai-banner-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.banner_link != blank %}
    <a href="{{ block.settings.banner_link }}" class="ai-banner-link-{{ ai_gen_id }}">
  {% endif %}
    <div class="ai-banner-image-wrapper-{{ ai_gen_id }}">
      {% if block.settings.desktop_image or block.settings.mobile_image %}
        {% if block.settings.desktop_image %}
          <img
            src="{{ block.settings.desktop_image | image_url: width: 2000 }}"
            alt="{{ block.settings.desktop_image.alt | escape }}"
            loading="lazy"
            width="{{ block.settings.desktop_image.width }}"
            height="{{ block.settings.desktop_image.height }}"
            class="ai-banner-image-{{ ai_gen_id }} ai-banner-desktop-image-{{ ai_gen_id }}"
          >
        {% endif %}
        
        {% if block.settings.mobile_image %}
          <img
            src="{{ block.settings.mobile_image | image_url: width: 1000 }}"
            alt="{{ block.settings.mobile_image.alt | escape }}"
            loading="lazy"
            width="{{ block.settings.mobile_image.width }}"
            height="{{ block.settings.mobile_image.height }}"
            class="ai-banner-image-{{ ai_gen_id }} ai-banner-mobile-image-{{ ai_gen_id }}"
          >
        {% else %}
          <img
            src="{{ block.settings.desktop_image | image_url: width: 1000 }}"
            alt="{{ block.settings.desktop_image.alt | escape }}"
            loading="lazy"
            width="{{ block.settings.desktop_image.width }}"
            height="{{ block.settings.desktop_image.height }}"
            class="ai-banner-image-{{ ai_gen_id }} ai-banner-mobile-image-{{ ai_gen_id }}"
          >
        {% endif %}
      {% else %}
        <div class="ai-banner-placeholder-{{ ai_gen_id }}">
          {{ 'hero-apparel-1' | placeholder_svg_tag }}
          <div class="ai-banner-empty-state-{{ ai_gen_id }}">
            Add desktop and mobile images to get started
          </div>
        </div>
      {% endif %}
    </div>

  {% if block.settings.banner_link != blank %}
    </a>
  {% endif %}
</div>

{% schema %}
{
  "name": "Banner image",
  "settings": [
    {
      "type": "header",
      "content": "Desktop image"
    },
    {
      "type": "image_picker",
      "id": "desktop_image",
      "label": "Desktop image"
    },
    {
      "type": "header",
      "content": "Mobile image"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Mobile image"
    },
    {
      "type": "header",
      "content": "Link"
    },
    {
      "type": "url",
      "id": "banner_link",
      "label": "Banner link"
    }
  ],
  "presets": [
    {
      "name": "Banner image"
    }
  ]
}
{% endschema %}
{% doc %}
  @prompt
    Create two sections. 
    in the first section, we need two divs of text .
    In the second section, it should be below the two divs. This section will contain two progress bars horizontally one over other. the first progress bar should be 30% and second progress bar should be 70 %, progress bars should be big and there should be a graph that represent the percentage of bars
    , modify the progress bar section...
    there should be a graph levels indicated 0 , 20%, 40%, 60%, 80% and 100% then we need to show both bars on the same graph. no need to show percentage inside the bars and in circle way
    , chart should be in horizontal direction and bars should be visible on it one above another in horizontal direction
    , chart is still in vertical, we need to show in horizontal 
    , Perfect, last update..
    bar text should be above the bars itself and bars should start from 0
    

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-text-progress-container-{{ ai_gen_id }} {
    padding: {{ block.settings.section_padding }}px;
    background-color: {{ block.settings.background_color }};
    max-width: 100%;
  }

  .ai-text-section-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.text_gap }}px;
    margin-bottom: {{ block.settings.section_spacing }}px;
  }

  .ai-text-div-{{ ai_gen_id }} {
    flex: 1;
    padding: {{ block.settings.text_padding }}px;
    /* background-color: {{ block.settings.text_background_color }}; */
    border-radius: {{ block.settings.text_border_radius }}px;
    color: {{ block.settings.text_color }};
  }

  .ai-text-div-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_font_size }}px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  .ai-text-div-content-{{ ai_gen_id }} {
    font-size: {{ block.settings.content_font_size }}px;
    line-height: 1.5;
    margin: 0;
  }

  .ai-progress-section-{{ ai_gen_id }} {
    /* background-color: {{ block.settings.chart_background_color }}; */
    border-radius: {{ block.settings.chart_border_radius }}px;
    padding: 36px 0;
  }

  .ai-chart-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.chart_title_font_size }}px;
    font-weight: 600;
    color: {{ block.settings.text_color }};
    margin: 0 0 {{ block.settings.chart_title_spacing }}px 0;
    text-align: center;
  }

  .ai-chart-container-{{ ai_gen_id }} {
    position: relative;
    padding: 20px 20px 40px 40px;
  }

  .ai-x-axis-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 29px;
    right: 9px;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .ai-x-axis-label-{{ ai_gen_id }} {
    font-size: {{ block.settings.grid_label_font_size }}px;
    color: #373131;
    font-weight: 500;
    padding-top: 8px;
  }

  .ai-chart-grid-{{ ai_gen_id }} {
    position: absolute;
    top: 20px;
    left: 40px;
    right: 19px;
    bottom: 40px;
    display: flex;
    justify-content: space-between;
  }

  .ai-grid-line-{{ ai_gen_id }} {
    width: 1px;
    background-color: {{ block.settings.grid_line_color }};
    height: 100%;
  }

  .ai-bars-container-{{ ai_gen_id }} {
    position: relative;
    padding: 20px 0px 40px 40px;
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.bars_gap }}px;
  }

  .ai-bar-wrapper-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ai-bar-labels-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .ai-bar-label-{{ ai_gen_id }} {
    font-size: 16px;
    font-weight: lighter;
    color: #000000d6;
    margin: 0;
  }

  .ai-bar-value-{{ ai_gen_id }} {
    font-size: {{ block.settings.bar_value_font_size }}px;
    color: {{ block.settings.text_color }};
    opacity: 0.8;
  }

  .ai-bar-track-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.bar_height }}px;
    /* background-color: {{ block.settings.bar_track_color }}; */
    border-radius: {{ block.settings.bar_border_radius }}px;
    position: relative;
    overflow: hidden;
  }

  .ai-bar-fill-{{ ai_gen_id }} {
    height: 100%;
    border-radius: {{ block.settings.bar_border_radius }}px;
    transition: width 2s ease;
    position: absolute;
    top: 0;
    right: 0;
  }

 .ai-bar-fill-1-{{ ai_gen_id }} {
    background: linear-gradient(90deg, #f3f3f3, #fff2e8);
  }

.ai-bar-fill-2-{{ ai_gen_id }} {
    background: linear-gradient(90deg, #007073, #0a6c53);
  } 

[class^="ai-bar-fill-"] {
  width: 0%;
  transition: width 2s ease;
}  

  .ai-legend-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: {{ block.settings.legend_gap }}px;
    margin-top: {{ block.settings.legend_spacing }}px;
  }

  .ai-legend-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .ai-legend-color-{{ ai_gen_id }} {
    width: {{ block.settings.legend_color_size }}px;
    height: {{ block.settings.legend_color_size }}px;
    border-radius: 2px;
  }

  .ai-legend-color-1-{{ ai_gen_id }} {
    background-color: {{ block.settings.progress_1_color }};
  }

  .ai-legend-color-2-{{ ai_gen_id }} {
    background-color: {{ block.settings.progress_2_color }};
  }

  .ai-legend-text-{{ ai_gen_id }} {
    font-size: 16px;
    color: #000000ab;
  }

  @media screen and (max-width: 749px) {
    .ai-text-section-{{ ai_gen_id }} {
      flex-direction: column;
      gap:0;
    }

  .ai-text-div-content-{{ ai_gen_id }} {
    font-size: 16px;
  }
  
  .ai-text-div-content-{{ ai_gen_id }} h3 {
    padding: 15px 0px;
    font-size: 24px;
    font-weight: 600;
  }

 .ai-text-div-al3bsbhrxz3fnmstzwaigenblockd7f06653jqnml:nth-child(1)
 {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}   

 .ai-text-div-al3bsbhrxz3fnmstzwaigenblockd7f06653jqnml:nth-child(2)
 {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}       

  .ai-text-div-{{ ai_gen_id }} {
    padding: 0 20px 10px;
  }

   .ai-text-progress-container-{{ ai_gen_id }} {
    padding: 16px;
  }

    .ai-chart-container-{{ ai_gen_id }} {
      padding: 50px 0px;
    }

    .ai-x-axis-{{ ai_gen_id }} {
      left: 20px;
      right: 20px;
    }

    .ai-chart-grid-{{ ai_gen_id }} {
      top: 20px;
      left: 20px;
      right: 20px;
      bottom: 30px;
    }

    .ai-bars-container-{{ ai_gen_id }} {
      padding: 20px 20px 30px 20px;
    }

    .ai-legend-{{ ai_gen_id }} {
      /* flex-direction: column; */
      align-items: center;
     padding-bottom: 20px;
    }

    .ai-progress-section-{{ ai_gen_id }} {
    padding:0px;
  }
    
  }
{% endstyle %}

<div class="ai-text-progress-container-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-text-section-{{ ai_gen_id }}">
    <div class="ai-text-div-{{ ai_gen_id }}">
      {% if block.settings.text_1_title != blank %}
        <h3 class="ai-text-div-title-{{ ai_gen_id }}">{{ block.settings.text_1_title }}</h3>
      {% endif %}
      {% if block.settings.text_1_content != blank %}
        <div class="ai-text-div-content-{{ ai_gen_id }}">{{ block.settings.text_1_content }}</div>
      {% endif %}
    </div>
    <div class="ai-text-div-{{ ai_gen_id }}">
      {% if block.settings.text_2_title != blank %}
        <h3 class="ai-text-div-title-{{ ai_gen_id }}">{{ block.settings.text_2_title }}</h3>
      {% endif %}
      {% if block.settings.text_2_content != blank %}
        <div class="ai-text-div-content-{{ ai_gen_id }}">{{ block.settings.text_2_content }}</div>
      {% endif %}
    </div>
  </div>

  <div class="ai-progress-section-{{ ai_gen_id }}">
    {% if block.settings.chart_title != blank %}
      <h3 class="ai-chart-title-{{ ai_gen_id }}">{{ block.settings.chart_title }}</h3>
    {% endif %}
    
    <div class="ai-chart-container-{{ ai_gen_id }}">
      <div class="ai-x-axis-{{ ai_gen_id }}">
        <span class="ai-x-axis-label-{{ ai_gen_id }}">0%</span>
        <span class="ai-x-axis-label-{{ ai_gen_id }}">20%</span>
        <span class="ai-x-axis-label-{{ ai_gen_id }}">40%</span>
        <span class="ai-x-axis-label-{{ ai_gen_id }}">60%</span>
        <span class="ai-x-axis-label-{{ ai_gen_id }}">80%</span>
        <span class="ai-x-axis-label-{{ ai_gen_id }}">100%</span>
      </div>

      <div class="ai-chart-grid-{{ ai_gen_id }}">
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
        <div class="ai-grid-line-{{ ai_gen_id }}"></div>
      </div>

      <div class="ai-bars-container-{{ ai_gen_id }}">
        <div class="ai-bar-wrapper-{{ ai_gen_id }}">
          <div class="ai-bar-labels-{{ ai_gen_id }}">
            <div class="ai-bar-label-{{ ai_gen_id }}">{{ block.settings.progress_1_label }}</div>
            <!-- <div class="ai-bar-value-{{ ai_gen_id }}">{{ block.settings.progress_1_value }}%</div> -->
          </div>
          <div class="ai-bar-track-{{ ai_gen_id }}">
            <div class="ai-bar-fill-{{ ai_gen_id }} ai-bar-fill-1-{{ ai_gen_id }}" data-width="{{ block.settings.progress_1_value }}%"></div>
          </div>
        </div>
        
        <div class="ai-bar-wrapper-{{ ai_gen_id }}">
          <div class="ai-bar-labels-{{ ai_gen_id }}">
            <div class="ai-bar-label-{{ ai_gen_id }}">{{ block.settings.progress_2_label }}</div>
            <!-- <div class="ai-bar-value-{{ ai_gen_id }}">{{ block.settings.progress_2_value }}%</div> -->
          </div>
          <div class="ai-bar-track-{{ ai_gen_id }}">
            <div class="ai-bar-fill-{{ ai_gen_id }} ai-bar-fill-2-{{ ai_gen_id }}" data-width="{{ block.settings.progress_2_value }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="ai-legend-{{ ai_gen_id }}">
      <div class="ai-legend-item-{{ ai_gen_id }}">
        <span class="ai-legend-text-{{ ai_gen_id }}">אחוזי ספיגה ביולוגית</span>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Text & Horizontal Chart",
  "settings": [
    {
      "type": "header",
      "content": "Text Section 1"
    },
    {
      "type": "text",
      "id": "text_1_title",
      "label": "Title",
      "default": "Performance Metrics"
    },
    {
      "type": "richtext",
      "id": "text_1_content",
      "label": "Content",
      "default": "<p>Track your progress with detailed analytics and visual representations of your achievements.</p>"
    },
    {
      "type": "header",
      "content": "Text Section 2"
    },
    {
      "type": "text",
      "id": "text_2_title",
      "label": "Title",
      "default": "Goal Achievement"
    },
    {
      "type": "html",
      "id": "text_2_content",
      "label": "Content",
      "default": "<p>Monitor your success rate and see how close you are to reaching your targets with our comprehensive dashboard.</p>"
    },
    {
      "type": "header",
      "content": "Chart Settings"
    },
    {
      "type": "text",
      "id": "chart_title",
      "label": "Chart title",
      "default": "Performance Comparison"
    },
    {
      "type": "header",
      "content": "Progress Bar 1"
    },
    {
      "type": "text",
      "id": "progress_1_label",
      "label": "Label",
      "default": "Sales Target"
    },
    {
      "type": "range",
      "id": "progress_1_value",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Progress value",
      "default": 30
    },
    {
      "type": "color",
      "id": "progress_1_color",
      "label": "Bar color",
      "default": "#000f9f"
    },
    {
      "type": "header",
      "content": "Progress Bar 2"
    },
    {
      "type": "text",
      "id": "progress_2_label",
      "label": "Label",
      "default": "Customer Satisfaction"
    },
    {
      "type": "range",
      "id": "progress_2_value",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Progress value",
      "default": 70
    },
    {
      "type": "color",
      "id": "progress_2_color",
      "label": "Bar color",
      "default": "#008060"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_background_color",
      "label": "Text box background",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "chart_background_color",
      "label": "Chart background color",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "grid_line_color",
      "label": "Grid line color",
      "default": "#e0e0e0"
    },
    {
      "type": "color",
      "id": "bar_track_color",
      "label": "Bar track color",
      "default": "#e8e8e8"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Section padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "section_spacing",
      "min": 10,
      "max": 80,
      "step": 2,
      "unit": "px",
      "label": "Space between sections",
      "default": 40
    },
    {
      "type": "range",
      "id": "text_gap",
      "min": 10,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Gap between text boxes",
      "default": 20
    },
    {
      "type": "range",
      "id": "text_padding",
      "min": 10,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Text box padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "text_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text box border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Chart Layout"
    },
    {
      "type": "range",
      "id": "chart_padding",
      "min": 20,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Chart padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "chart_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Chart border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "bars_gap",
      "min": 20,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Gap between bars",
      "default": 40
    },
    {
      "type": "range",
      "id": "bar_height",
      "min": 30,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Bar height",
      "default": 50
    },
    {
      "type": "range",
      "id": "bar_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Bar border radius",
      "default": 6
    },
    {
      "type": "range",
      "id": "legend_spacing",
      "min": 16,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Legend spacing",
      "default": 24
    },
    {
      "type": "range",
      "id": "legend_gap",
      "min": 20,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Legend gap",
      "default": 32
    },
    {
      "type": "range",
      "id": "legend_color_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Legend color size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_font_size",
      "min": 16,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Title font size",
      "default": 20
    },
    {
      "type": "range",
      "id": "content_font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Content font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "chart_title_font_size",
      "min": 18,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Chart title font size",
      "default": 24
    },
    {
      "type": "range",
      "id": "chart_title_spacing",
      "min": 16,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Chart title spacing",
      "default": 24
    },
    {
      "type": "range",
      "id": "grid_label_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Grid label font size",
      "default": 12
    },
    {
      "type": "range",
      "id": "bar_label_font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Bar label font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "bar_value_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Bar value font size",
      "default": 12
    },
    {
      "type": "range",
      "id": "legend_font_size",
      "min": 12,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Legend font size",
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Text & Horizontal Chart"
    }
  ]
}
{% endschema %}
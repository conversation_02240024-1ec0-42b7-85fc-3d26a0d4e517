{% doc %}
  @prompt
    Create a section in which left div contains slider of images. show one full image and the image that will come appears in half. on click of navigation second image will be full and third will appear as half in the place of second. 
    Show title and description on image, initially show title and on hover show description below that.
    on right div, we have a heading , description and button, slider should move from right to left, slides should move left to right. and height of the images should be 500px, div positions should be same as I described, slider div will be on left side

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .product .border-style.custom-section-content {
    margin: 0;
    width: 100%;
  }

  .ai-image-content-block-{{ ai_gen_id }} {
    display: flex;
    flex-direction: row;
    gap: 40px;
    overflow: hidden;
    width: 100%;
    padding-right: 120px;
  }

  @media screen and (max-width: 990px) {
    .ai-image-content-block-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 30px;
    }
  }

  .ai-slider-container-{{ ai_gen_id }} {
    width: 50%;
    position: relative;
    overflow: hidden;
  }

  .ai-slider-track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.5s ease-in-out;
    width: 100%;
    max-width: 890px;
    height: auto;
    gap:35.27px;
    will-change: transform;
    direction: rtl;
  } 

  @media screen and (max-width: 990px) {
    .ai-slider-container-{{ ai_gen_id }} {
      width: 100%;
    }
     .ai-slider-track-{{ ai_gen_id }} {
      display: flex;
      transition: transform 0.5s ease-in-out;
      width: 100%;
      height: auto;
      gap:27px;
    }
  }  

  .ai-slide-{{ ai_gen_id }} {
    user-select: none;
    min-width: calc(100% - {{ block.settings.next_slide_visible }}%);
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.image_border_radius }}px;
    height: 500px;
  }

  .ai-slide-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-slide-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f4;
  }

  .ai-slide-placeholder-{{ ai_gen_id }} svg {
    width: 50%;
    height: 50%;
  }

  .ai-slide-content-{{ ai_gen_id }} {
    position: absolute;
    left: 0;
    right: 0;
    padding: 16px;
    background: rgb(0 0 0 / 4%);
    backdrop-filter: blur(18px);
    -webkit-backdrop-filter: blur(18px);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    bottom: 15px;
    width: 95%;
    border-radius: 10px;
    margin: 0 auto;
    transition: transform 0.3s ease;
    direction: rtl;
  }

  .ai-slide-title-{{ ai_gen_id }} {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }

  .ai-slide-description-{{ ai_gen_id }} {
    margin-top: 10px;
    font-size: 16px;
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: opacity 0.3s ease, max-height 0.3s ease;
  }

  .ai-slide-{{ ai_gen_id }}:hover .ai-slide-description-{{ ai_gen_id }} {
    opacity: 1;
    max-height: 200px;
  }

  .ai-slider-nav-{{ ai_gen_id }} {
    display: flex;
    justify-content: flex-end;
    gap: 0px;
    margin-top: 20px;
  }

  .ai-slider-button-{{ ai_gen_id }} {
    background-color: {{ block.settings.nav_button_color }};
    color: {{ block.settings.nav_button_text_color }};
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  /* .ai-slider-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.nav_button_hover_color }};
  } */

  /* .ai-slider-button-{{ ai_gen_id }}:focus {
    outline: 2px solid {{ block.settings.nav_button_color }};
    outline-offset: 2px;
  } */

/* .ai-next-button-{{ ai_gen_id }} {
    transform: rotate(180deg);
} */
 .ai-prev-button-{{ ai_gen_id }}:disabled,
.ai-next-button-{{ ai_gen_id }}:disabled {
  opacity: 0.3;
  pointer-events: none;
}

/* .ai-slider-button-{{ ai_gen_id }} svg {
    width: 1.5rem;
    height: 1.3rem;
} */

  .ai-content-container-{{ ai_gen_id }} {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: right;
    align-items: flex-end;
  }

  @media screen and (max-width: 990px) {
    .ai-content-container-{{ ai_gen_id }} {
      width: 100%;
      padding: 0 20px;
      order: -1;
    }
  }

  .ai-content-heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    margin-bottom: 20px;
    color: {{ block.settings.heading_color }};
    font-weight: 600;
  }

  .ai-content-description-{{ ai_gen_id }} {
    margin-bottom: 30px;
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    line-height: 1.5;
  }

  .ai-content-button-{{ ai_gen_id }} {
    display: inline-flex;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    font-weight: 500;
    transition: background-color 0.3s ease;
  }


  .ai-dots-container-{{ ai_gen_id }} {
    display: none;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
  }

  .ai-dot-{{ ai_gen_id }} {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: {{ block.settings.dot_color }};
    opacity: 0.5;
    cursor: pointer;
    transition: opacity 0.3s ease;
  }

  .ai-dot-{{ ai_gen_id }}.active {
    opacity: 1;
  }
  a.arrow-button {
    display: flex;
    flex-direction: row-reverse;
    gap: 10px;
}
.ai-slider-track-{{ ai_gen_id }} {
  display: flex;
  transition: transform 0.5s ease-in-out;
  will-change: transform;
}

@media (max-width: 767px) {
  .ai-image-content-block-{{ ai_gen_id }} {
    width: 100%;
    display: display: flex;
  }

  .ai-slider-container-{{ ai_gen_id }} {
    width: 100%;
    padding-right: 16px;
    padding-left: 16px; 
    box-sizing: border-box;
  }
  
  .ai-slider-track-{{ ai_gen_id }} {
    height: auto;
    gap: 12px; 
    display: flex;
    flex-wrap: nowrap;
  }
  
  .ai-slide-{{ ai_gen_id }} {
    height: 360px;
    width: 281px;
    min-width: 281px;
    flex-shrink: 0; 
    margin: 0 !important;
  }
  
  .ai-slide-image-{{ ai_gen_id }} {
    width: 281px;
    height: 360px;
    object-fit: cover;
  }
  
  .ai-slide-content-{{ ai_gen_id }} {
    bottom: 10px; 
    padding: 12px; 
    width: 90%; 
    font-size: 14px; 
  }
  
  .ai-slide-title-{{ ai_gen_id }} {
    font-size: 20px; 
    line-height: 1.2; 
  }
  
  .ai-slide-description-{{ ai_gen_id }} {
    font-size: 12px; /
    margin-top: 6px;
  }
  
  .ai-slider-nav-{{ ai_gen_id }} {
    margin-top: 15px; 
    padding: 0 !important;
  }
}

{% endstyle %}

<image-slider-with-content-{{ ai_gen_id }} 
  class="ai-image-content-block-{{ ai_gen_id }}" 
  {{ block.shopify_attributes }}
>
  <div class="ai-slider-container-{{ ai_gen_id }}">
    <div class="ai-slider-track-{{ ai_gen_id }}">
      {% for i in (1..10) reversed %}
        {% if i <= 5 %}
          {% if i == 1 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image.value %}
            {% assign current_title = product.metafields.custom.meta_image.value.title.value %}
            {% assign current_description = product.metafields.custom.meta_image.value.description %}
          {% elsif i == 2 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_2.value %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_2.value %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_2 %}
          {% elsif i == 3 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_3.value %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_3.value %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_3 %}
          {% elsif i == 4 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_4.value %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_4.value %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_4 %}
          {% elsif i == 5 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_5.value %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_5.value %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_5 %}
          {% endif %}
        {% else %}
          {% comment %} For slides 6-10, try to get from metaobject, fallback to block settings {% endcomment %}
          {% assign image_field = 'image_' | append: i %}
          {% assign title_field = 'title_' | append: i %}
          {% assign description_field = 'description_' | append: i %}

          {% if i == 6 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_6.value | default: block.settings[image_field] %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_6.value | default: block.settings[title_field] %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_6 | default: block.settings[description_field] %}
          {% elsif i == 7 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_7.value | default: block.settings[image_field] %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_7.value | default: block.settings[title_field] %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_7 | default: block.settings[description_field] %}
          {% elsif i == 8 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_8.value | default: block.settings[image_field] %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_8.value | default: block.settings[title_field] %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_8 | default: block.settings[description_field] %}
          {% elsif i == 9 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_9.value | default: block.settings[image_field] %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_9.value | default: block.settings[title_field] %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_9 | default: block.settings[description_field] %}
          {% elsif i == 10 %}
            {% assign current_image = product.metafields.custom.meta_image.value.slide_image_10.value | default: block.settings[image_field] %}
            {% assign current_title = product.metafields.custom.meta_image.value.title_10.value | default: block.settings[title_field] %}
            {% assign current_description = product.metafields.custom.meta_image.value.description_10 | default: block.settings[description_field] %}
          {% endif %}
        {% endif %}

        {% if current_image != blank or forloop.index == 0 %}
          <div class="ai-slide-{{ ai_gen_id }}" data-index="{{ forloop.index0 }}">
            {% if current_image != blank %}
              <img
                src="{{ current_image | image_url: width: 1000 }}"
                alt="{{ current_image.alt | escape }}"
                class="ai-slide-image-{{ ai_gen_id }}"
                loading="{% if forloop.index <= 2 %}eager{% else %}lazy{% endif %}"
              >
            {% else %}
              <div class="ai-slide-placeholder-{{ ai_gen_id }}">
                {{ 'image' | placeholder_svg_tag }}
              </div>
            {% endif %}

            <div class="ai-slide-content-{{ ai_gen_id }}">
              {% if current_title != blank %}
                <h3 class="ai-slide-title-{{ ai_gen_id }}">
                  {{ current_title }}
                </h3>
              {% endif %}
              {% if current_description != blank %}
                <div class="ai-slide-description-{{ ai_gen_id }}">
                  {{ current_description | metafield_tag }}
                </div>
              {% endif %}
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
    
    <div class="ai-slider-nav-{{ ai_gen_id }}">
      <button class="ai-slider-button-{{ ai_gen_id }} ai-next-button-{{ ai_gen_id }}" aria-label="Next slide">
        {{ 'arrow-fig.svg' | inline_asset_content }}
      </button>

      <button class="ai-slider-button-{{ ai_gen_id }} ai-prev-button-{{ ai_gen_id }}" aria-label="Previous slide">
      {{ 'arrow-fig.svg' | inline_asset_content }}

      </button>   
    </div>
    
    <div class="ai-dots-container-{{ ai_gen_id }}"></div>
  </div>
  
  <div class="ai-content-container-{{ ai_gen_id }}">
    <h2 class="ai-content-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
    <div class="ai-content-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
    {% if block.settings.button_text != blank and block.settings.button_link != blank %}
      <a style="background: transparent;" href="{{ block.settings.button_link }}" class="arrow-button">
        {{ block.settings.button_text }} <img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Frame_1984077742.png?v=1750315065" style="width: auto;">
      </a>
    {% endif %}
  </div>
</image-slider-with-content-{{ ai_gen_id }}>

<!-- <script>
  (function() {
    class ImageSliderWithContent extends HTMLElement {
      constructor() {
        super();
        this.sliderTrack = this.querySelector('.ai-slider-track-{{ ai_gen_id }}');
        this.slides = this.querySelectorAll('.ai-slide-{{ ai_gen_id }}');
        this.prevButton = this.querySelector('.ai-prev-button-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-next-button-{{ ai_gen_id }}');
        this.dotsContainer = this.querySelector('.ai-dots-container-{{ ai_gen_id }}');

        this.currentIndex = 0;
        this.slideWidth = 0;
        this.slideCount = this.slides.length;

        if (this.slideCount > 0) {
          this.init();
        }
      }

      init() {
        this.createDots();
        this.updateSlideWidth();
        this.updateSlider();

        this.prevButton.addEventListener('click', () => this.goToPrev());
        this.nextButton.addEventListener('click', () => this.goToNext());

        window.addEventListener('resize', () => {
          this.updateSlideWidth();
          this.updateSlider(false);
        });
      }

      createDots() {
        for (let i = 0; i < this.slideCount; i++) {
          const dot = document.createElement('div');
          dot.classList.add('ai-dot-{{ ai_gen_id }}');
          if (i === 0) dot.classList.add('active');

          dot.addEventListener('click', () => this.goToSlide(i));
          this.dotsContainer.appendChild(dot);
        }
        this.dots = this.dotsContainer.querySelectorAll('.ai-dot-{{ ai_gen_id }}');
      }

      updateSlideWidth() {
        if (this.slides.length > 0) {
          const slideStyle = window.getComputedStyle(this.slides[0]);
          this.slideWidth = this.slides[0].offsetWidth +
                            parseFloat(slideStyle.marginLeft) +
                            parseFloat(slideStyle.marginRight);
        }
      }

      updateSlider(animate = true) {
        if (!this.sliderTrack) return;

        this.sliderTrack.style.transition = animate ? 'transform 0.5s ease-in-out' : 'none';

        // RTL behavior — move right as index increases
        const offset = (this.slideCount - 1 - this.currentIndex) * this.slideWidth;
        this.sliderTrack.style.transform = `translateX(-${offset}px)`;

        // Update dots
        if (this.dots) {
          this.dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentIndex);
          });
        }

        this.prevButton.disabled = this.currentIndex === 0;
        this.nextButton.disabled = this.currentIndex === this.slideCount - 1;

        if (!animate) {
          setTimeout(() => {
            this.sliderTrack.style.transition = 'transform 0.5s ease-in-out';
          }, 50);
        }
      }

      goToSlide(index) {
        if (index < 0) index = 0;
        if (index >= this.slideCount) index = this.slideCount - 1;
        this.currentIndex = index;
        this.updateSlider();
      }

      goToNext() {
        this.goToSlide(this.currentIndex + 1);
      }

      goToPrev() {
        this.goToSlide(this.currentIndex - 1);
      }
    }

    customElements.define('image-slider-with-content-{{ ai_gen_id }}', ImageSliderWithContent);
  })();
</script> -->

<!-- <script>
  (function() {
    class ImageSliderWithContent extends HTMLElement {
      constructor() {
        super();
        this.sliderTrack = this.querySelector('.ai-slider-track-{{ ai_gen_id }}');
        this.slides = this.querySelectorAll('.ai-slide-{{ ai_gen_id }}');
        this.prevButton = this.querySelector('.ai-prev-button-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-next-button-{{ ai_gen_id }}');
        this.dotsContainer = this.querySelector('.ai-dots-container-{{ ai_gen_id }}');

        this.currentIndex = 0;
        this.slideWidth = 0;
        this.slideCount = this.slides.length;

        // Drag state
        this.isDragging = true;
        this.startX = 0;
        this.endX = 0;

        if (this.slideCount > 0) {
          this.init();
        }
      }

      init() {
        this.createDots();
        this.updateSlideWidth();
        this.updateSlider();

        this.prevButton.addEventListener('click', () => this.goToPrev());
        this.nextButton.addEventListener('click', () => this.goToNext());

        // Mouse drag events
        this.sliderTrack.addEventListener('mousedown', this.onDragStart.bind(this));
        this.sliderTrack.addEventListener('mousemove', this.onDragMove.bind(this));
        this.sliderTrack.addEventListener('mouseup', this.onDragEnd.bind(this));
        this.sliderTrack.addEventListener('mouseleave', this.onDragEnd.bind(this));

        // Prevent default drag behavior on images
        this.slides.forEach(slide => {
          slide.addEventListener('dragstart', e => e.preventDefault());
        });

        window.addEventListener('resize', () => {
          this.updateSlideWidth();
          this.updateSlider(false);
        });
      }

      onDragStart(e) {
        this.isDragging = true;
        this.startX = e.clientX;
        this.sliderTrack.style.cursor = 'grabbing';
      }

      onDragMove(e) {
        if (!this.isDragging) return;
        this.endX = e.clientX;
      }

      onDragEnd() {
        if (!this.isDragging) return;
        this.isDragging = false;
        const delta = this.endX - this.startX;

        if (Math.abs(delta) > 50) {
          if (delta < 0 && this.currentIndex < this.slideCount - 1) {
            this.goToNext();
          } else if (delta > 0 && this.currentIndex > 0) {
            this.goToPrev();
          }
        }

        this.sliderTrack.style.cursor = 'grab';
        this.startX = this.endX = 0;
      }

      createDots() {
        for (let i = 0; i < this.slideCount; i++) {
          const dot = document.createElement('div');
          dot.classList.add('ai-dot-{{ ai_gen_id }}');
          if (i === 0) dot.classList.add('active');

          dot.addEventListener('click', () => this.goToSlide(i));
          this.dotsContainer.appendChild(dot);
        }
        this.dots = this.dotsContainer.querySelectorAll('.ai-dot-{{ ai_gen_id }}');
      }

      updateSlideWidth() {
        if (this.slides.length > 0) {
          const slideStyle = window.getComputedStyle(this.slides[0]);
          this.slideWidth = this.slides[0].offsetWidth +
                            parseFloat(slideStyle.marginLeft) +
                            parseFloat(slideStyle.marginRight);
        }
      }

      updateSlider(animate = true) {
        if (!this.sliderTrack) return;

        this.sliderTrack.style.transition = animate ? 'transform 0.5s ease-in-out' : 'none';

        const offset = (this.slideCount - 1 - this.currentIndex) * this.slideWidth;
        this.sliderTrack.style.transform = `translateX(-${offset}px)`;

        if (this.dots) {
          this.dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentIndex);
          });
        }

        this.prevButton.disabled = this.currentIndex === 0;
        this.nextButton.disabled = this.currentIndex === this.slideCount - 1;

        if (!animate) {
          setTimeout(() => {
            this.sliderTrack.style.transition = 'transform 0.5s ease-in-out';
          }, 50);
        }
      }

      goToSlide(index) {
        if (index < 0) index = 0;
        if (index >= this.slideCount) index = this.slideCount - 1;
        this.currentIndex = index;
        this.updateSlider();
      }

      goToNext() {
        this.goToSlide(this.currentIndex + 1);
      }

      goToPrev() {
        this.goToSlide(this.currentIndex - 1);
      }
    }

    customElements.define('image-slider-with-content-{{ ai_gen_id }}', ImageSliderWithContent);
  })();
</script> -->


<script>
  (function() {
  class ImageSliderWithContent extends HTMLElement {
    constructor() {
      super();      
      this.sliderTrack = this.querySelector('.ai-slider-track-{{ ai_gen_id }}');
      this.slides = this.querySelectorAll('.ai-slide-{{ ai_gen_id }}');
      this.prevButton = this.querySelector('.ai-prev-button-{{ ai_gen_id }}');
      this.nextButton = this.querySelector('.ai-next-button-{{ ai_gen_id }}');
      this.dotsContainer = this.querySelector('.ai-dots-container-{{ ai_gen_id }}');

      this.currentIndex = 0;      
      this.slideWidth = 0;
      this.slideCount = this.slides.length;

      this.startX = 0;
      this.endX = 0;
      this.isDragging = false;
      this.isTouch = false;

      if (this.slideCount > 0) {
        this.init();
      }
    }

    init() {
      this.createDots();
      this.updateSlideWidth();
      this.currentIndex = this.slideCount - 1;
      this.updateSlider();

      this.prevButton.addEventListener('click', () => this.goToPrev());
      this.nextButton.addEventListener('click', () => this.goToNext());

      // Mouse drag
      this.sliderTrack.addEventListener('mousedown', this.onDragStart.bind(this));
      this.sliderTrack.addEventListener('mousemove', this.onDragMove.bind(this));
      this.sliderTrack.addEventListener('mouseup', this.onDragEnd.bind(this));
      this.sliderTrack.addEventListener('mouseleave', this.onDragEnd.bind(this));

      // Touch drag
      this.sliderTrack.addEventListener('touchstart', this.onTouchStart.bind(this), { passive: true });
      this.sliderTrack.addEventListener('touchmove', this.onTouchMove.bind(this), { passive: true });
      this.sliderTrack.addEventListener('touchend', this.onTouchEnd.bind(this));

      // Prevent image dragging
      this.slides.forEach(slide => {
        slide.addEventListener('dragstart', e => e.preventDefault());
      });

      window.addEventListener('resize', () => {
        this.updateSlideWidth();
        this.updateSlider(false);
      });
    }

    onDragStart(e) {
      this.isDragging = true;
      this.startX = e.clientX;
      this.sliderTrack.style.cursor = 'grabbing';
    }

    onDragMove(e) {
      if (!this.isDragging) return;
      this.endX = e.clientX;
    }

    onDragEnd() {
      if (!this.isDragging) return;
      this.isDragging = false;
      const delta = this.endX - this.startX;
      this.handleSwipe(delta);
      this.sliderTrack.style.cursor = 'grab';
      this.startX = this.endX = 0;
    }

    onTouchStart(e) {
      this.isTouch = true;
      this.startX = e.touches[0].clientX;
    }

    onTouchMove(e) {
      if (!this.isTouch) return;
      this.endX = e.touches[0].clientX;
    }

    onTouchEnd() {
      if (!this.isTouch) return;
      this.isTouch = false;
      const delta = this.endX - this.startX;
      this.handleSwipe(delta);
      this.startX = this.endX = 0;
    }

    handleSwipe(delta) {
      const isRTL = true; // RTL
      if (Math.abs(delta) > 50) {
        if (isRTL) {
          if (delta > 0 && this.currentIndex > 0) {
            this.goToNext();
          } else if (delta < 0 && this.currentIndex < this.slideCount - 1) {
            this.goToPrev();
          }
        } else {
          if (delta < 0 && this.currentIndex < this.slideCount - 1) {
            this.goToNext();
          } else if (delta > 0 && this.currentIndex > 0) {
            this.goToPrev();
          }
        }
      }
    }

    createDots() {
      if (!this.dotsContainer) return;
      for (let i = 0; i < this.slideCount; i++) {
        const dot = document.createElement('div');
        dot.classList.add('ai-dot-{{ ai_gen_id }}');
        if (i === this.slideCount - 1) dot.classList.add('active');
        dot.addEventListener('click', () => this.goToSlide(i));
        this.dotsContainer.appendChild(dot);
      }
      this.dots = this.dotsContainer.querySelectorAll('.ai-dot-{{ ai_gen_id }}');
    }

    updateSlideWidth() {
      if (this.slides.length > 0) {
        const style = window.getComputedStyle(this.sliderTrack);
        const gap = parseFloat(style.gap) || 0; 
        const slideWidth = this.slides[0].offsetWidth;       
        this.slideWidth = this.slides[0].offsetWidth + gap;
        console.log('SlideWidth updated:', this.slideWidth, 'Gap:', gap, 'Slide offsetWidth:', this.slides[0].offsetWidth);
      }
    }

    updateSlider(animate = true) {
      if (!this.sliderTrack) return;

      this.sliderTrack.style.transition = animate ? 'transform 0.5s ease-in-out' : 'none';      
      const offset = (this.slideCount - 1 - this.currentIndex) * this.slideWidth;
      this.sliderTrack.style.transform = `translateX(${offset}px)`; 

      console.log('Offset:', offset, 'CurrentIndex:', this.currentIndex, 'SlideWidth:', this.slideWidth, 'SlideCount:', this.slideCount);
      
      if (this.dots) {
        this.dots.forEach((dot, index) => {
          dot.classList.toggle('active', index === this.currentIndex);
        });
      }

      this.prevButton.disabled = this.currentIndex === this.slideCount - 1;
      this.nextButton.disabled = this.currentIndex === 0; 

      if (!animate) {
        setTimeout(() => {
          this.sliderTrack.style.transition = 'transform 0.5s ease-in-out';
        }, 50);
      };     
    }

    goToSlide(index) {
      if (index < 0) index = 0;
      if (index >= this.slideCount) index = this.slideCount - 1;
      this.currentIndex = index;
      this.updateSlider();
    }

    goToNext() {
      this.goToSlide(this.currentIndex - 1); 
    }

    goToPrev() {
      this.goToSlide(this.currentIndex + 1); 
    }

  }

  customElements.define('image-slider-with-content-{{ ai_gen_id }}', ImageSliderWithContent);
})();

  // (function() {
  //   class ImageSliderWithContent extends HTMLElement {
  //     constructor() {
  //       super();
  //       this.sliderTrack = this.querySelector('.ai-slider-track-{{ ai_gen_id }}');
  //       this.slides = this.querySelectorAll('.ai-slide-{{ ai_gen_id }}');
  //       this.prevButton = this.querySelector('.ai-prev-button-{{ ai_gen_id }}');
  //       this.nextButton = this.querySelector('.ai-next-button-{{ ai_gen_id }}');
  //       this.dotsContainer = this.querySelector('.ai-dots-container-{{ ai_gen_id }}');

  //       this.currentIndex = 0;
  //       this.slideWidth = 0;
  //       this.slideCount = this.slides.length;

  //       this.startX = 0;
  //       this.endX = 0;
  //       this.isDragging = false;
  //       this.isTouch = false;

  //       if (this.slideCount > 0) {
  //         this.init();
  //       }
  //     }

  //     init() {
  //       this.createDots();
  //       this.updateSlideWidth();
  //       this.updateSlider();

  //       this.prevButton.addEventListener('click', () => this.goToPrev());
  //       this.nextButton.addEventListener('click', () => this.goToNext());

  //       // Mouse drag
  //       this.sliderTrack.addEventListener('mousedown', this.onDragStart.bind(this));
  //       this.sliderTrack.addEventListener('mousemove', this.onDragMove.bind(this));
  //       this.sliderTrack.addEventListener('mouseup', this.onDragEnd.bind(this));
  //       this.sliderTrack.addEventListener('mouseleave', this.onDragEnd.bind(this));

  //       // Touch drag
  //       this.sliderTrack.addEventListener('touchstart', this.onTouchStart.bind(this), { passive: true });
  //       this.sliderTrack.addEventListener('touchmove', this.onTouchMove.bind(this), { passive: true });
  //       this.sliderTrack.addEventListener('touchend', this.onTouchEnd.bind(this));

  //       // Prevent image dragging
  //       this.slides.forEach(slide => {
  //         slide.addEventListener('dragstart', e => e.preventDefault());
  //       });

  //       window.addEventListener('resize', () => {
  //         this.updateSlideWidth();
  //         this.updateSlider(false);
  //       });
  //     }

  //     // Mouse events
  //     onDragStart(e) {
  //       this.isDragging = true;
  //       this.startX = e.clientX;
  //       this.sliderTrack.style.cursor = 'grabbing';
  //     }

  //     onDragMove(e) {
  //       if (!this.isDragging) return;
  //       this.endX = e.clientX;
  //     }

  //     onDragEnd() {
  //       if (!this.isDragging) return;
  //       this.isDragging = false;
  //       const delta = this.endX - this.startX;
  //       this.handleSwipe(delta);
  //       this.sliderTrack.style.cursor = 'grab';
  //       this.startX = this.endX = 0;
  //     }

  //     // Touch events
  //     onTouchStart(e) {
  //       this.isTouch = true;
  //       this.startX = e.touches[0].clientX;
  //     }

  //     onTouchMove(e) {
  //       if (!this.isTouch) return;
  //       this.endX = e.touches[0].clientX;
  //     }

  //     onTouchEnd() {
  //       if (!this.isTouch) return;
  //       this.isTouch = false;
  //       const delta = this.endX - this.startX;
  //       this.handleSwipe(delta);
  //       this.startX = this.endX = 0;
  //     }

  //     // Handle swipe in RTL
  //     handleSwipe(delta) {
  //       const isRTL = true; // force RTL

  //       if (Math.abs(delta) > 50) {
  //         if (isRTL) {
  //           if (delta > 0 && this.currentIndex < this.slideCount - 1) {
  //             this.goToNext();
  //           } else if (delta < 0 && this.currentIndex > 0) {
  //             this.goToPrev();
  //           }
  //         } else {
  //           if (delta < 0 && this.currentIndex < this.slideCount - 1) {
  //             this.goToNext();
  //           } else if (delta > 0 && this.currentIndex > 0) {
  //             this.goToPrev();
  //           }
  //         }
  //       }
  //     }

  //     createDots() {
  //       for (let i = 0; i < this.slideCount; i++) {
  //         const dot = document.createElement('div');
  //         dot.classList.add('ai-dot-{{ ai_gen_id }}');
  //         if (i === 0) dot.classList.add('active');

  //         dot.addEventListener('click', () => this.goToSlide(i));
  //         this.dotsContainer.appendChild(dot);
  //       }
  //       this.dots = this.dotsContainer.querySelectorAll('.ai-dot-{{ ai_gen_id }}');
  //     }

  //     updateSlideWidth() {
  //       if (this.slides.length > 0) {
  //         const style = window.getComputedStyle(this.slides[0]);
  //         this.slideWidth =
  //           this.slides[0].offsetWidth +
  //           parseFloat(style.marginLeft) +
  //           parseFloat(style.marginRight);
  //       }
  //     }

  //     updateSlider(animate = true) {
  //       if (!this.sliderTrack) return;

  //       this.sliderTrack.style.transition = animate ? 'transform 0.5s ease-in-out' : 'none';

  //       // RTL logic
  //       const offset = (this.slideCount - 1 - this.currentIndex) * this.slideWidth;
  //       this.sliderTrack.style.transform = `translateX(-${offset}px)`;

  //       if (this.dots) {
  //         this.dots.forEach((dot, index) => {
  //           dot.classList.toggle('active', index === this.currentIndex);
  //         });
  //       }

  //       this.prevButton.disabled = this.currentIndex === 0;
  //       this.nextButton.disabled = this.currentIndex === this.slideCount - 1;

  //       if (!animate) {
  //         setTimeout(() => {
  //           this.sliderTrack.style.transition = 'transform 0.5s ease-in-out';
  //         }, 50);
  //       }
  //     }

  //     goToSlide(index) {
  //       if (index < 0) index = 0;
  //       if (index >= this.slideCount) index = this.slideCount - 1;
  //       this.currentIndex = index;
  //       this.updateSlider();
  //     }

  //     goToNext() {
  //       this.goToSlide(this.currentIndex + 1);
  //     }

  //     goToPrev() {
  //       this.goToSlide(this.currentIndex - 1);
  //     }
  //   }

  //   customElements.define('image-slider-with-content-{{ ai_gen_id }}', ImageSliderWithContent);
  // })();
</script>



{% schema %}
{
  "name": "Image Slider with Content",
  "tag": null,
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "range",
      "id": "next_slide_visible",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "%",
      "label": "Next slide visibility",
      "default": 30
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Image border radius",
      "default": 8
    },
    {
      "type": "color",
      "id": "nav_button_color",
      "label": "Navigation button color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "nav_button_text_color",
      "label": "Navigation button icon color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "nav_button_hover_color",
      "label": "Navigation button hover color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "dot_color",
      "label": "Dot indicator color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Featured Collection"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Showcase your best products with this beautiful slider and content block. Add your own description here."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Shop Now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#333333"
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    },
    {
      "type": "header",
      "content": "Slide 1"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Slide 1 Title"
    },
    {
      "type": "richtext",
      "id": "description_1",
      "label": "Description",
      "default": "<p>Description for slide 1. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 2"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Slide 2 Title"
    },
    {
      "type": "richtext",
      "id": "description_2",
      "label": "Description",
      "default": "<p>Description for slide 2. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 3"
    },
    {
      "type": "image_picker",
      "id": "image_3",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Slide 3 Title"
    },
    {
      "type": "richtext",
      "id": "description_3",
      "label": "Description",
      "default": "<p>Description for slide 3. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 4"
    },
    {
      "type": "image_picker",
      "id": "image_4",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_4",
      "label": "Title",
      "default": "Slide 4 Title"
    },
    {
      "type": "richtext",
      "id": "description_4",
      "label": "Description",
      "default": "<p>Description for slide 4. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 5"
    },
    {
      "type": "image_picker",
      "id": "image_5",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_5",
      "label": "Title",
      "default": "Slide 5 Title"
    },
    {
      "type": "richtext",
      "id": "description_5",
      "label": "Description",
      "default": "<p>Description for slide 5. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 6"
    },
    {
      "type": "image_picker",
      "id": "image_6",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_6",
      "label": "Title",
      "default": "Slide 6 Title"
    },
    {
      "type": "richtext",
      "id": "description_6",
      "label": "Description",
      "default": "<p>Description for slide 6. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 7"
    },
    {
      "type": "image_picker",
      "id": "image_7",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_7",
      "label": "Title",
      "default": "Slide 7 Title"
    },
    {
      "type": "richtext",
      "id": "description_7",
      "label": "Description",
      "default": "<p>Description for slide 7. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 8"
    },
    {
      "type": "image_picker",
      "id": "image_8",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_8",
      "label": "Title",
      "default": "Slide 8 Title"
    },
    {
      "type": "richtext",
      "id": "description_8",
      "label": "Description",
      "default": "<p>Description for slide 8. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 9"
    },
    {
      "type": "image_picker",
      "id": "image_9",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_9",
      "label": "Title",
      "default": "Slide 9 Title"
    },
    {
      "type": "richtext",
      "id": "description_9",
      "label": "Description",
      "default": "<p>Description for slide 9. This text appears on hover.</p>"
    },
    {
      "type": "header",
      "content": "Slide 10"
    },
    {
      "type": "image_picker",
      "id": "image_10",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title_10",
      "label": "Title",
      "default": "Slide 10 Title"
    },
    {
      "type": "richtext",
      "id": "description_10",
      "label": "Description",
      "default": "<p>Description for slide 10. This text appears on hover.</p>"
    }
  ],
  "presets": [
    {
      "name": "Image Slider with Content"
    }
  ]
}
{% endschema %}
<style>
  .ai-prev-button-{{ ai_gen_id }} svg {
  transform: rotate(180deg);
}
    
</style>
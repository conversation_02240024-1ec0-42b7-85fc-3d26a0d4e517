{% doc %}
  @prompt
    create a video carasouel in which 4 videos will be shown at the load and change the video on navigation click .. the count of visibility will be 4 , on click of next first slide hide and 5th slide appear..
    Videos have title and description over it bottom

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-carousel-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .ai-video-carousel__container-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-video-carousel__track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.5s ease;
    gap: {{ block.settings.gap }}px;
  }

  .ai-video-carousel__slide-{{ ai_gen_id }} {
    flex: 0 0 calc(25% - {{ block.settings.gap | times: 3 | divided_by: 4 }}px);
    position: relative;
    border-radius: {{ block.settings.slide_border_radius }}px;
    overflow: hidden;
    background-color: #000;
  }

  .ai-video-carousel__video-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.video_height }}px;
    object-fit: cover;
    display: block;
  }

  .ai-video-carousel__video-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.video_height }}px;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-video-carousel__video-placeholder-{{ ai_gen_id }} svg {
    width: 60px;
    height: 60px;
    opacity: 0.5;
  }

  .ai-video-carousel__overlay-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 30px 15px 15px;
    color: white;
  }

  .ai-video-carousel__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    font-weight: 600;
    margin: 0 0 8px;
    line-height: 1.2;
  }

  .ai-video-carousel__description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }

  .ai-video-carousel__nav-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: {{ block.settings.nav_color }};
    color: {{ block.settings.nav_icon_color }};
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 2;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .ai-video-carousel__nav-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.nav_hover_color }};
    transform: translateY(-50%) scale(1.1);
  }

  .ai-video-carousel__nav-{{ ai_gen_id }}:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%) scale(1);
  }

  .ai-video-carousel__nav--prev-{{ ai_gen_id }} {
    left: -25px;
  }

  .ai-video-carousel__nav--next-{{ ai_gen_id }} {
    right: -25px;
  }

  .ai-video-carousel__nav-{{ ai_gen_id }} svg {
    width: 20px;
    height: 20px;
  }

  .ai-video-carousel__indicators-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
  }

  .ai-video-carousel__indicator-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: {{ block.settings.indicator_color }};
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.4;
  }

  .ai-video-carousel__indicator-{{ ai_gen_id }}.active {
    opacity: 1;
    transform: scale(1.2);
  }

  @media screen and (max-width: 1024px) {
    .ai-video-carousel__slide-{{ ai_gen_id }} {
      flex: 0 0 calc(50% - {{ block.settings.gap | divided_by: 2 }}px);
    }
  }

  @media screen and (max-width: 768px) {
    .ai-video-carousel__slide-{{ ai_gen_id }} {
      flex: 0 0 100%;
    }
    
    .ai-video-carousel__nav--prev-{{ ai_gen_id }} {
      left: 10px;
    }
    
    .ai-video-carousel__nav--next-{{ ai_gen_id }} {
      right: 10px;
    }

    .ai-video-carousel__video-{{ ai_gen_id }},
    .ai-video-carousel__video-placeholder-{{ ai_gen_id }} {
      height: {{ block.settings.video_height | times: 0.75 }}px;
    }
  }
{% endstyle %}

<video-carousel-{{ ai_gen_id }} class="ai-video-carousel-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-video-carousel__container-{{ ai_gen_id }}">
    <div class="ai-video-carousel__track-{{ ai_gen_id }}" data-track>
      {% for i in (1..8) %}
        {% liquid
          assign video_key = 'video_' | append: i
          assign title_key = 'title_' | append: i
          assign description_key = 'description_' | append: i
          
          assign video = block.settings[video_key]
          assign title = block.settings[title_key]
          assign description = block.settings[description_key]
        %}
        
        {% if video != blank or title != blank or description != blank %}
          <div class="ai-video-carousel__slide-{{ ai_gen_id }}" data-slide="{{ forloop.index0 }}">
            {% if video != blank %}
              <video 
                class="ai-video-carousel__video-{{ ai_gen_id }}"
                {% if block.settings.autoplay %}autoplay{% endif %}
                {% if block.settings.loop %}loop{% endif %}
                {% if block.settings.muted %}muted{% endif %}
                {% if block.settings.controls %}controls{% endif %}
                playsinline
              >
                <source src="{{ video }}" type="video/mp4">
                Your browser does not support the video tag.
              </video>
            {% else %}
              <div class="ai-video-carousel__video-placeholder-{{ ai_gen_id }}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="5,3 19,12 5,21"></polygon>
                </svg>
              </div>
            {% endif %}
            
            {% if title != blank or description != blank %}
              <div class="ai-video-carousel__overlay-{{ ai_gen_id }}">
                {% if title != blank %}
                  <h3 class="ai-video-carousel__title-{{ ai_gen_id }}">{{ title }}</h3>
                {% endif %}
                {% if description != blank %}
                  <p class="ai-video-carousel__description-{{ ai_gen_id }}">{{ description }}</p>
                {% endif %}
              </div>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>
    
    <button class="ai-video-carousel__nav-{{ ai_gen_id }} ai-video-carousel__nav--prev-{{ ai_gen_id }}" data-prev>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="15,18 9,12 15,6"></polyline>
      </svg>
    </button>
    
    <button class="ai-video-carousel__nav-{{ ai_gen_id }} ai-video-carousel__nav--next-{{ ai_gen_id }}" data-next>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="9,18 15,12 9,6"></polyline>
      </svg>
    </button>
  </div>
  
  <div class="ai-video-carousel__indicators-{{ ai_gen_id }}" data-indicators></div>
</video-carousel-{{ ai_gen_id }}>

<script>
(function() {
  class VideoCarousel{{ ai_gen_id }} extends HTMLElement {
    constructor() {
      super();
      this.currentIndex = 0;
      this.visibleSlides = 4;
      this.totalSlides = 0;
    }

    connectedCallback() {
      this.track = this.querySelector('[data-track]');
      this.prevBtn = this.querySelector('[data-prev]');
      this.nextBtn = this.querySelector('[data-next]');
      this.indicatorsContainer = this.querySelector('[data-indicators]');
      this.slides = this.querySelectorAll('[data-slide]');
      
      this.totalSlides = this.slides.length;
      
      if (this.totalSlides === 0) return;
      
      this.setupEventListeners();
      this.createIndicators();
      this.updateCarousel();
      this.updateResponsiveSlides();
      
      window.addEventListener('resize', () => this.updateResponsiveSlides());
    }

    updateResponsiveSlides() {
      const width = window.innerWidth;
      if (width <= 768) {
        this.visibleSlides = 1;
      } else if (width <= 1024) {
        this.visibleSlides = 2;
      } else {
        this.visibleSlides = 4;
      }
      this.updateCarousel();
    }

    setupEventListeners() {
      this.prevBtn.addEventListener('click', () => this.goToPrev());
      this.nextBtn.addEventListener('click', () => this.goToNext());
    }

    createIndicators() {
      const maxIndex = Math.max(0, this.totalSlides - this.visibleSlides);
      this.indicatorsContainer.innerHTML = '';
      
      for (let i = 0; i <= maxIndex; i++) {
        const indicator = document.createElement('button');
        indicator.className = 'ai-video-carousel__indicator-{{ ai_gen_id }}';
        indicator.addEventListener('click', () => this.goToSlide(i));
        this.indicatorsContainer.appendChild(indicator);
      }
    }

    goToPrev() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
        this.updateCarousel();
      }
    }

    goToNext() {
      const maxIndex = Math.max(0, this.totalSlides - this.visibleSlides);
      if (this.currentIndex < maxIndex) {
        this.currentIndex++;
        this.updateCarousel();
      }
    }

    goToSlide(index) {
      const maxIndex = Math.max(0, this.totalSlides - this.visibleSlides);
      this.currentIndex = Math.min(index, maxIndex);
      this.updateCarousel();
    }

    updateCarousel() {
      const slideWidth = 100 / this.visibleSlides;
      const translateX = -(this.currentIndex * slideWidth);
      
      this.track.style.transform = `translateX(${translateX}%)`;
      
      const maxIndex = Math.max(0, this.totalSlides - this.visibleSlides);
      this.prevBtn.disabled = this.currentIndex === 0;
      this.nextBtn.disabled = this.currentIndex >= maxIndex;
      
      const indicators = this.indicatorsContainer.querySelectorAll('.ai-video-carousel__indicator-{{ ai_gen_id }}');
      indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === this.currentIndex);
      });
    }
  }

  customElements.define('video-carousel-{{ ai_gen_id }}', VideoCarousel{{ ai_gen_id }});
})();
</script>

{% schema %}
{
  "name": "Video Carousel",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "video_height",
      "min": 200,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Video height",
      "default": 300
    },
    {
      "type": "range",
      "id": "gap",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Gap between slides",
      "default": 16
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Container border radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "slide_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Slide border radius",
      "default": 8
    },
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay videos",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop videos",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Mute videos",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Title size",
      "default": 16
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "color",
      "id": "nav_color",
      "label": "Navigation background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "nav_hover_color",
      "label": "Navigation hover background",
      "default": "#f0f0f0"
    },
    {
      "type": "color",
      "id": "nav_icon_color",
      "label": "Navigation icon color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "indicator_color",
      "label": "Indicator color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Video 1"
    },
    {
      "type": "url",
      "id": "video_1",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title",
      "default": "Video Title 1"
    },
    {
      "type": "textarea",
      "id": "description_1",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 2"
    },
    {
      "type": "url",
      "id": "video_2",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title",
      "default": "Video Title 2"
    },
    {
      "type": "textarea",
      "id": "description_2",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 3"
    },
    {
      "type": "url",
      "id": "video_3",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title",
      "default": "Video Title 3"
    },
    {
      "type": "textarea",
      "id": "description_3",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 4"
    },
    {
      "type": "url",
      "id": "video_4",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_4",
      "label": "Title",
      "default": "Video Title 4"
    },
    {
      "type": "textarea",
      "id": "description_4",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 5"
    },
    {
      "type": "url",
      "id": "video_5",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_5",
      "label": "Title",
      "default": "Video Title 5"
    },
    {
      "type": "textarea",
      "id": "description_5",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 6"
    },
    {
      "type": "url",
      "id": "video_6",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_6",
      "label": "Title",
      "default": "Video Title 6"
    },
    {
      "type": "textarea",
      "id": "description_6",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 7"
    },
    {
      "type": "url",
      "id": "video_7",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_7",
      "label": "Title",
      "default": "Video Title 7"
    },
    {
      "type": "textarea",
      "id": "description_7",
      "label": "Description",
      "default": "Video description goes here"
    },
    {
      "type": "header",
      "content": "Video 8"
    },
    {
      "type": "url",
      "id": "video_8",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "title_8",
      "label": "Title",
      "default": "Video Title 8"
    },
    {
      "type": "textarea",
      "id": "description_8",
      "label": "Description",
      "default": "Video description goes here"
    }
  ],
  "presets": [
    {
      "name": "Video Carousel"
    }
  ]
}
{% endschema %}
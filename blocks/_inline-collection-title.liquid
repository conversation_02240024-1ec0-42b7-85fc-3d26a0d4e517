
{%- doc -%}
  @param {string} [suffix] - The suffix to add to the collection title
{%- enddoc -%}

{% if block.settings.collection == blank and request.design_mode %}
  {% assign collection_title = 'placeholders.collection_title' | t %}
{% elsif block.settings.collection != blank %}
  {% assign collection_title = block.settings.collection.title %}
{% endif %}

{% assign plain_text = collection_title | strip_newlines | strip_html | strip %}

{% # We need to make sure the wrapper is rendered even if the text element is empty in the theme editor to allow live text editing %}
{%- if plain_text != '' or request.design_mode -%}
  <span
    class="
      text-block
      custom-typography
      {% if block.settings.weight != blank %}custom-font-weight{% endif %}
      {% if block.settings.font_size != blank %}custom-font-size{% endif %}
    "
    style="{% render 'typography-style', preset: 'custom', settings: block.settings, type: 'heading' %}"
    {{ block.shopify_attributes }}
  >
    <span>{{ collection_title }}</span>{{ suffix }}
  </span>
{%- endif -%}

{% schema %}
{
  "name": "t:names.collection_title",
  "tag": null,
  "settings": [
    {
      "type": "collection",
      "id": "collection",
      "label": "t:settings.collection"
    },
    {
      "type": "header",
      "content": "t:content.typography"
    },
    {
      "type": "select",
      "id": "font",
      "label": "t:settings.font",
      "options": [
        {
          "value": "var(--font-body--family)",
          "label": "t:options.body"
        },
        {
          "value": "var(--font-subheading--family)",
          "label": "t:options.subheading"
        },
        {
          "value": "var(--font-heading--family)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--font-accent--family)",
          "label": "t:options.accent"
        }
      ],
      "default": "var(--font-body--family)"
    },
    {
      "type": "select",
      "id": "weight",
      "label": "t:settings.weight",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "100",
          "label": "t:options.thin"
        },
        {
          "value": "300",
          "label": "t:options.light"
        },
        {
          "value": "400",
          "label": "t:options.regular"
        },
        {
          "value": "500",
          "label": "t:options.medium"
        },
        {
          "value": "600",
          "label": "t:options.semibold"
        },
        {
          "value": "700",
          "label": "t:options.bold"
        },
        {
          "value": "900",
          "label": "t:options.black"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "line_height",
      "label": "t:settings.line_height",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal"
    },
    {
      "type": "select",
      "id": "letter_spacing",
      "label": "t:settings.letter_spacing",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    }
  ]
}
{% endschema %}

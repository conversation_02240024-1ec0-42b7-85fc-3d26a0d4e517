

<div
  class="page-block spacing-style{% if block.settings.inherit_color_scheme == false %} color-{{ block.settings.color_scheme }}{% endif %}"
  style="{% render 'spacing-padding', settings: block.settings %}{% if block.settings.background %} background-color: {{ block.settings.background_color }};{% endif %}"
>
  {%- if block.settings.page != blank -%}
    {%- if block.settings.page.title != blank -%}
      <h2 class="page-title">{{ block.settings.page.title }}</h2>
    {%- endif -%}
    {%- if block.settings.page.content != blank -%}
      <div class="rte">
        {{ block.settings.page.content }}
      </div>
    {%- endif -%}
  {%- else -%}
    <h2 class="page-title">{{ 'content.page_placeholder_title' | t }}</h2>
    <div class="rte">
      {{ 'content.page_placeholder_content' | t }}
    </div>
  {%- endif -%}
</div>

{% stylesheet %}
  .page-block {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: auto;
    align-items: flex-start;
  }

  .page-title {
    margin-bottom: var(--margin-xl);
  }

  .placeholder-image {
    position: relative;
    aspect-ratio: var(--ratio);
    overflow: hidden;
  }

  .page-placeholder {
    width: 100%;
    height: 100%;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.page",
  "settings": [
    {
      "type": "page",
      "id": "page",
      "label": "t:settings.page"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "checkbox",
      "id": "background",
      "label": "t:settings.background",
      "default": false
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "t:settings.background_color",
      "default": "#00000026",
      "visible_if": "{{ block.settings.background }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.page",
      "category": "t:categories.basic"
    }
  ]
}
{% endschema %}

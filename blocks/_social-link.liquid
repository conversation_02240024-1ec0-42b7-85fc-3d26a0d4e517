

{% liquid
  if block.settings.link != blank
    # Extract domain from URL
    assign platform = block.settings.link | split: '//' | last | remove: 'www.' | split: '.' | first
  endif
%}

<div
  class="social-icons__icon-wrapper"
  {{ block.shopify_attributes }}
>
  <a
    href="{{ block.settings.link }}"
    target="_blank"
    rel="noopener noreferrer"
  >
    <span class="social-icons__icon-label">{{ platform | capitalize }}</span>
    <svg
      class="social-icons__icon"
      aria-hidden="true"
      focusable="false"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
    >
      {%- render 'icon', icon: platform -%}
    </svg>
  </a>
</div>

{% stylesheet %}
  .social-icons__icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--icon-size-lg);
  }

  .social-icons__icon {
    display: flex;
    fill: currentColor;
    flex-shrink: 0;
    width: var(--icon-size-lg);
    height: var(--icon-size-lg);
  }

  .social-icons__icon {
    display: none;
  }

  .social-icons__icon-wrapper:has(.social-icons__icon path) {
    width: var(--icon-size-lg);

    .social-icons__icon {
      display: block;
    }

    .social-icons__icon-label {
      display: none;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.social_link",
  "tag": null,
  "settings": [
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    }
  ],
  "presets": [
    {
      "name": "t:names.social_link",
      "category": "t:categories.footer"
    }
  ]
}
{% endschema %}

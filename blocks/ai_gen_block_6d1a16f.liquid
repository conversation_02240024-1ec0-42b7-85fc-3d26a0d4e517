{% doc %}
  @prompt
    blogs grid section 

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-blogs-grid-{{ ai_gen_id }} {
    padding: {{ block.settings.section_padding_top }}px 0 {{ block.settings.section_padding_bottom }}px;
  }

  .ai-blogs-grid-container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
    padding: 0px;
  }

  .ai-blogs-grid-header-{{ ai_gen_id }} {
    text-align: {{ block.settings.text_alignment }};
    margin-bottom: 40px;
  }

  .ai-blogs-grid-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin: 0 0 16px;
  }

  .ai-blogs-grid-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    color: {{ block.settings.text_color }};
    margin: 0;
  }

  .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat({{ block.settings.columns_desktop }}, 1fr);
    gap: {{ block.settings.grid_gap }}px;
  }

  .ai-blog-card-{{ ai_gen_id }} {
    background-color: {{ block.settings.card_background }};
    border-radius: {{ block.settings.card_border_radius }}px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    /* {% if block.settings.show_card_shadow %}
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    {% endif %} */
  }

  .ai-blog-card-{{ ai_gen_id }}:hover {
    /* transform: translateY(-4px); */
    /* {% if block.settings.show_card_shadow %}
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    {% endif %} */
  }

  .ai-blog-card-image-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.image_height }}px;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
  }

  .ai-blog-card-image-{{ ai_gen_id }} img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .ai-blog-card-{{ ai_gen_id }}:hover .ai-blog-card-image-{{ ai_gen_id }} img {
    transform: scale(1.05);
  }

  .ai-blog-card-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-blog-card-image-placeholder-{{ ai_gen_id }} svg {
    width: 60px;
    height: 60px;
    opacity: 0.5;
  }

  .ai-blog-card-content-{{ ai_gen_id }} {
    padding: 16px 0;
  }

  .ai-blog-card-meta-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 12px;
    color: {{ block.settings.meta_color }};
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ai-blog-card-date-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .ai-blog-card-author-{{ ai_gen_id }} {
    display: none;
    align-items: center;
    gap: 4px;
  }

  .ai-blog-card-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.card_title_size }}px;
    color: {{ block.settings.card_title_color }};
    margin: 0 0 12px;
    line-height: 1.3;
    font-weight : 700;
  }

  .ai-blog-card-title-{{ ai_gen_id }} a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .ai-blog-card-title-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-blog-card-excerpt-{{ ai_gen_id }} {
    font-size: {{ block.settings.excerpt_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.5;
    margin: 0 0 16px;
    font-weight: 500;
  }

  .ai-blog-card-link-{{ ai_gen_id }} {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: {{ block.settings.link_color }};
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
    flex-direction: row-reverse;
  }

  .ai-blog-card-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.link_hover_color }};
  }

  .ai-blog-card-link-{{ ai_gen_id }} svg {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
  }

  .ai-blog-card-link-{{ ai_gen_id }}:hover svg {
    transform: translateX(4px);
  }

   @media screen and (max-width: 768px) {
    .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
      grid-template-columns: repeat({{ block.settings.columns_mobile }}, 1fr);
      gap: {{ block.settings.grid_gap | times: 0.75 }}px;
    }

    .ai-blog-card-content-{{ ai_gen_id }} {
      padding: {{ block.settings.card_padding | times: 0.75 }}px 0;
    }

    .ai-blogs-grid-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.8 }}px;
    }

    .ai-blog-card-image-{{ ai_gen_id }} {
      height: {{ block.settings.image_height | times: 0.8 }}px;
    }
  } 
{% comment %}
   @media screen and (max-width: 768px) {
  .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
    display: flex;
    gap: 2px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    padding-block: 10px;
    margin: 0px -55px;
  }

  /* hide scrollbar */
  .ai-blogs-grid-wrapper-{{ ai_gen_id }}::-webkit-scrollbar {
    display: none;
  }
  .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .ai-blog-card-{{ ai_gen_id }} {
    flex: 0 0 88%; /* one card takes ~80% of screen */
    scroll-snap-align: start; /* ✅ aligns card to edge */
  }

  /* LTR layout */
  [dir="ltr"] .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
    flex-direction: row;
    padding-left: 0;    /* first card flush left */
    padding-right: 16px; /* breathing room at end */
  }

  /* RTL layout */
  [dir="rtl"] .ai-blogs-grid-wrapper-{{ ai_gen_id }} {
    flex-direction: row-reverse;
    padding-right: 0;   /* first card flush right */
    padding-left: 16px; /* breathing room at end */
  }

  .ai-blog-card-content-{{ ai_gen_id }} {
    padding: {{ block.settings.card_padding | times: 0.75 }}px 0;
  }

  .ai-blogs-grid-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size | times: 0.8 }}px;
  }

  .ai-blog-card-image-{{ ai_gen_id }} {
    height: {{ block.settings.image_height | times: 0.8 }}px;
  }
}
{% endcomment %}
{% endstyle %}

<div class="ai-blogs-grid-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-blogs-grid-container-{{ ai_gen_id }}">
    {% if block.settings.heading != blank or block.settings.description != blank %}
      <div class="ai-blogs-grid-header-{{ ai_gen_id }}">
        {% if block.settings.heading != blank %}
          <h2 class="ai-blogs-grid-title-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
        {% endif %}
        {% if block.settings.description != blank %}
          <div class="ai-blogs-grid-description-{{ ai_gen_id }}">{{ block.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    <div class="ai-blogs-grid-wrapper-{{ ai_gen_id }}">
      {% if block.settings.blog != blank %}
        {% for article in block.settings.blog.articles limit: block.settings.articles_to_show %}
          <article class="ai-blog-card-{{ ai_gen_id }}">
            <div class="ai-blog-card-image-{{ ai_gen_id }}">
              {% if article.image %}
              <a href="{{ article.url }}">  <img
                  src="{{ article.image | image_url: width: 600 }}"
                  alt="{{ article.image.alt | escape }}"
                  loading="lazy"
                  width="600"
                  height="{{ block.settings.image_height }}"
                > </a>
              {% else %}
                <div class="ai-blog-card-image-placeholder-{{ ai_gen_id }}">
                  {{ 'blog-apparel-1' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>

            <div class="ai-blog-card-content-{{ ai_gen_id }}">
              {% if block.settings.show_meta %}
                <div class="ai-blog-card-meta-{{ ai_gen_id }}">
                  <div class="ai-blog-card-date-{{ ai_gen_id }}">
                {% comment %}  
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    {{ article.published_at | date: '%B %d, %Y' }}
                {% endcomment %}
                {{ article.metafields.custom.min_reading }}
                  </div>
                  {% if article.author != blank %}
                    <div class="ai-blog-card-author-{{ ai_gen_id }}">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                      {{ article.author }}
                    </div>
                  {% endif %}
                </div>
              {% endif %}

              <h3 class="ai-blog-card-title-{{ ai_gen_id }}">
                <a href="{{ article.url }}">{{ article.title }}</a>
              </h3>

              {% if block.settings.show_excerpt and article.excerpt != blank %}
                <div class="ai-blog-card-excerpt-{{ ai_gen_id }}">
                  {{ article.excerpt | truncate: block.settings.excerpt_length }}
                </div>
              {% endif %}

              {% if block.settings.show_read_more %}
                <a href="{{ article.url }}" class="ai-blog-card-link-{{ ai_gen_id }}">
             <span class="blog-icon"><img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Frame_1984077742_1.png?v=1750752289"/></span>  {{ block.settings.read_more_text }}
                </a>
              {% endif %}
            </div>
          </article>
        {% endfor %}
      {% else %}
        {% for i in (1..block.settings.articles_to_show) %}
          <article class="ai-blog-card-{{ ai_gen_id }}">
            <div class="ai-blog-card-image-{{ ai_gen_id }}">
              <div class="ai-blog-card-image-placeholder-{{ ai_gen_id }}">
                {{ 'blog-apparel-1' | placeholder_svg_tag }}
              </div>
            </div>

            <div class="ai-blog-card-content-{{ ai_gen_id }}">
              {% if block.settings.show_meta %}
                <div class="ai-blog-card-meta-{{ ai_gen_id }}">
                  <div class="ai-blog-card-date-{{ ai_gen_id }}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    January 1, 2024
                  </div>
                  <div class="ai-blog-card-author-{{ ai_gen_id }}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    Author Name
                  </div>
                </div>
              {% endif %}

              <h3 class="ai-blog-card-title-{{ ai_gen_id }}">
                <a href="#">Sample Blog Post Title</a>
              </h3>

              {% if block.settings.show_excerpt %}
                <div class="ai-blog-card-excerpt-{{ ai_gen_id }}">
                  This is a sample excerpt for the blog post. It provides a brief overview of what readers can expect from the full article.
                </div>
              {% endif %}

              {% if block.settings.show_read_more %}
                <a href="#" class="ai-blog-card-link-{{ ai_gen_id }}">
                <span class="blog-icon"><img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Frame_1984077742_1.png?v=1750752289"/></span>  {{ block.settings.read_more_text }}
                </a>
              {% endif %}
            </div>
          </article>
        {% endfor %}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Blogs Grid",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "Blog"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Latest Articles"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Discover our latest insights and stories</p>"
    },
    {
      "type": "range",
      "id": "articles_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "label": "Articles to show",
      "default": 6
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Columns on desktop",
      "options": [
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        },
        {
          "value": "4",
          "label": "4"
        }
      ],
      "default": "3"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "grid_gap",
      "min": 10,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Grid gap",
      "default": 24
    },
    {
      "type": "header",
      "content": "Card Settings"
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 150,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Image height",
      "default": 240
    },
    {
      "type": "range",
      "id": "card_padding",
      "min": 10,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Card padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "card_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Card border radius",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "show_card_shadow",
      "label": "Show card shadow",
      "default": true
    },
    {
      "type": "header",
      "content": "Article Display"
    },
    {
      "type": "checkbox",
      "id": "show_meta",
      "label": "Show date and author",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "label": "Show excerpt",
      "default": true
    },
    {
      "type": "range",
      "id": "excerpt_length",
      "min": 50,
      "max": 200,
      "step": 10,
      "label": "Excerpt length",
      "default": 120
    },
    {
      "type": "checkbox",
      "id": "show_read_more",
      "label": "Show read more link",
      "default": true
    },
    {
      "type": "text",
      "id": "read_more_text",
      "label": "Read more text",
      "default": "Read More"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Card background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "meta_color",
      "label": "Meta text color",
      "default": "#999999"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link color",
      "default": "#000f9f"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "range",
      "id": "card_title_size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "label": "Card title size",
      "default": 20
    },
    {
      "type": "range",
      "id": "excerpt_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Excerpt size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "presets": [
    {
      "name": "Blogs Grid"
    }
  ]
}
{% endschema %}
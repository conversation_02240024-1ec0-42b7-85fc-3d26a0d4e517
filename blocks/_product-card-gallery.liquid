

{% capture children %}
  {% unless block.settings.product == blank %}
    {% render 'product-card-badges', product: block.settings.product %}
    {%  if settings.quick_add or settings.mobile_quick_add %}
      {% render 'quick-add', product: block.settings.product, section_id: section.id %}
    {% endif %}
  {% endunless %}
{% endcapture %}

{% render 'card-gallery', children: children %}

{% # Title and price for the zoomed out grid view %}
<div class="product-grid-view-zoom-out--details">
  {% if block.settings.product == blank %}
    <h3 class="h4">{{ 'content.product_card_placeholder' | t }}</h3>
  {% else %}
    <h3 class="h4">{{ block.settings.product.title }}</h3>
    <div class="h6">
      <product-price data-product-id="{{ block.settings.product.id }}">
        {% render 'price', product_resource: block.settings.product, show_unit_price: false %}
      </product-price>
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "t:names.product_image",
  "tag": null,
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "t:settings.product"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "portrait",
          "label": "t:options.portrait"
        },
        {
          "value": "square",
          "label": "t:options.square"
        },
        {
          "value": "landscape",
          "label": "t:options.landscape"
        }
      ],
      "default": "portrait",
      "label": "t:settings.aspect_ratio",
      "info": "t:info.aspect_ratio_adjusted"
    },
    {
      "type": "header",
      "content": "t:content.borders"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.style",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "t:settings.thickness",
      "default": 1,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.opacity",
      "default": 100,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 32,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.product_card_media",
      "category": "t:categories.product",
      "settings": {
        "product": "{{ closest.product }}"
      }
    }
  ]
}
{% endschema %}

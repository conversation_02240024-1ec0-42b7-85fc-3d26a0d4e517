

{% assign button_collection = block.settings.collection %}
{% assign button_url = button_collection.url %}

{% if button_collection.products.size > section.settings.max_products %}
  {% render 'button', link: button_url %}
{% endif %}

{% schema %}
{
  "name": "t:names.product_list_button",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.visible_if_collection_has_more_products"
    },
    {
      "type": "text",
      "id": "label",
      "label": "t:settings.label",
      "default": "t:text_defaults.button_label"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "t:settings.collection"
    },
    {
      "type": "checkbox",
      "id": "open_in_new_tab",
      "label": "t:settings.open_new_tab",
      "default": false
    },
    {
      "type": "select",
      "id": "style_class",
      "label": "t:settings.style",
      "options": [
        {
          "value": "button",
          "label": "t:options.primary"
        },
        {
          "value": "button-secondary",
          "label": "t:options.secondary"
        },
        {
          "value": "link",
          "label": "t:options.link"
        }
      ],
      "default": "button"
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width_desktop",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == \"custom\" }}"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:settings.width_mobile",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width_mobile",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width_mobile == \"custom\" }}"
    }
  ],
  "presets": [
    {
      "name": "t:names.product_list_button",
      "category": "t:categories.collection",
      "settings": {
        "label": "View all",
        "collection": "{{ closest.collection }}",
        "style_class": "button-secondary"
      }
    }
  ]
}
{% endschema %}

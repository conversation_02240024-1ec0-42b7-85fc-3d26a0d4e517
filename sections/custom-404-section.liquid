{% schema %}
{
  "name": "Custom 404 Section",
  "tag": "section",
  "class": "custom-404-section",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "404 Image"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Page Not Found"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Sorry, the page you are looking for doesn’t exist or has been moved."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Back to Home"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link",
      "default": "/"
    }
  ],
  "presets": [
    {
      "name": "Custom 404 Section",
      "category": "404"
    }
  ]
}
{% endschema %}

{% stylesheet %}
.custom-404-section {
  text-align: center;
  padding: 0 20px 60px;
  position: relative;
  margin-top: -100px;
}
@media screen and (max-width: 767px){
  .custom-404-section {
    padding: 0 20px 40px;
    margin-top: -60px;
  }
}

.custom-404-section .custom-404-image {
  max-width: 500px;
  width: 100%;
  height: auto;
  margin: 0 auto 25px;
}

.custom-404-section h2 {
  color: #323438;
  font-size: 48px;
  line-height: 48px;
  font-weight: 700;
  letter-spacing: 0.3px;
  margin: 0 0 24px;
}

.custom-404-section p {
  color: #323438;
  font-size: 16px;
  line-height: 1.63;
  font-weight: 500;
  margin-bottom: 24px;
}

.custom-404-section .custom-404__btnWrap  .icon__button {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  border-radius: 8px;
  padding: 12px 24px;
  background: #007A73;
  color: #ffffff;
}
.custom-404-section .custom-404__btnWrap  .icon__button .btn__icon{
  width: 20px;
}
.custom-404-section .custom-404__btnWrap  .icon__button .btn__text{
  font-size: 16px;
  line-height: 1.3;
  font-weight: 700;
}

.custom-404-section .custom-404__btnWrap .icon__button:hover {
  background: #065954;
}
{% endstylesheet %}

<div class="custom-404-section">
  {% if section.settings.image != blank %}
    <div class="custom-404-image">
      <img src="{{ section.settings.image | image_url: width: 500 }}" alt="404 Image" loading="lazy">
    </div>
  {% endif %}

  <div class="custom-404__descWrap">
    {% if section.settings.title != blank %}
      <h2>{{ section.settings.title }}</h2>
    {% endif %}
  
    {% if section.settings.description != blank %}
      <p>{{ section.settings.description }}</p>
    {% endif %}
  
    {% if section.settings.button_text != blank and section.settings.button_link != blank %}
      <div class="custom-404__btnWrap">
        <a href="{{ section.settings.button_link }}" class="icon__button">
          <span class="btn__icon">
            <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </span>
          <span class="btn__text">
            {{ section.settings.button_text }}
          </span>
        </a>
      </div>
    {% endif %}
  </div>
</div>

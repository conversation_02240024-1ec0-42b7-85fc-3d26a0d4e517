{% comment %}
  Section: SP Blog Posts
  Features:
  - Select blog and number of posts
  - Grid layout on desktop (configurable columns + gap)
  - Mobile carousel with Glide.js (configurable items per view, gap, peek)
  - Article meta toggles: date, author, tags, excerpt, read more
{% endcomment %}

<section class="sp__blogPostsWrapper" id="sp-blog-posts-{{ section.id }}">
  <div class="sp-container">
    {% if section.settings.heading != blank or section.settings.button_label != blank %}
        <div class="sp__blogHeaderWrap">
            <div class="sp__blogHeaderInner">
                {% if section.settings.heading != blank %}
                    <div class="desc__wrap">
                        <h2>{{ section.settings.heading }}</h2>
                    </div>
                {% endif %}
                
                {% if section.settings.button_label != blank %}
                    <div class="btn__wrap">
                        <a href="{{ section.settings.button_link }}" class="sp__iconButton">
                            <span class="btn__text">{{ section.settings.button_label }}</span>
                            <span class="btn__icon">
                                <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </span>
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}

    {% assign blog_handle = section.settings.blog %}
    {% assign articles_limit = section.settings.articles_limit %}
    {% if blogs[blog_handle] and blogs[blog_handle].articles_count > 0 %}
        <!-- Desktop Grid -->
        <div class="sp__blogArticlesDesktopWrap">
            <div class="sp__blogGridWrapper"
                style="grid-template-columns: repeat({{ section.settings.desktop_columns }}, 1fr); gap: {{ section.settings.desktop_gap }}px;">
                {% for article in blogs[blog_handle].articles limit: articles_limit %}
                {% render 'sp-article-card', article: article, section: section %}
                {% endfor %}
            </div>
        </div>

        <!-- Mobile Slider -->
        <div class="sp__blogArticlesMobileWrap">
            <div class="sp__blogCarousel glide" id="glide-{{ section.id }}">
                <div class="glide__track" data-glide-el="track">
                    <ul class="glide__slides">
                    {% for article in blogs[blog_handle].articles limit: articles_limit %}
                        <li class="glide__slide">
                        {% render 'sp-article-card', article: article, section: section %}
                        </li>
                    {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    {% endif %}

    {% if section.settings.button_label != blank %}
        <div class="sp__blogFooterWrap">
            {% if section.settings.button_label != blank %}
                <div class="btn__wrap">
                    <a href="{{ section.settings.button_link }}" class="sp__iconButton">
                        <span class="btn__text">{{ section.settings.button_label }}</span>
                        <span class="btn__icon">
                            <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </span>
                    </a>
                </div>
            {% endif %}
        </div>
    {% endif %}
  </div>
</section>

<script src="https://cdn.jsdelivr.net/npm/@glidejs/glide/dist/glide.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function(){
    var glide = document.getElementById("glide-{{ section.id }}");
    if(glide){
      new Glide(glide, {
        type: 'slider',
        rewind: false,
        startAt: 0,
        perView: {{ section.settings.slider_items }},
        touchRatio: 1,
        gap: {{ section.settings.carousel_gap }},
        //animationTimingFunc: 'ease-in-out',
        animationDuration: 400,
        direction: 'rtl',
        peek: { before: 0, after: {{ section.settings.peak_next }} },
        breakpoints: {
          768: { perView: {{ section.settings.slider_items }} }
        }
      }).mount();
    }
  });
</script>

<style>
    .sp__blogPostsWrapper{
        padding: 40px 0 100px;
    }
    .sp__blogHeaderWrap{
        padding: 0 120px;
        margin-bottom: 40px;
        direction: rtl;
    }
    @media screen and (max-width: 767px){
        .sp__blogHeaderWrap{
            padding: 0 20px;
            margin-bottom: 40px;
        }
    }
    .sp__blogHeaderWrap .sp__blogHeaderInner{
        display: flex;
        align-items: center;
    }
    .sp__blogHeaderWrap .sp__blogHeaderInner .desc__wrap{
        width: calc(100% - 200px);
        padding-left: 30px;
    }
    @media screen and (max-width: 767px){
        .sp__blogHeaderWrap .sp__blogHeaderInner .desc__wrap{
            width: 100%;
            padding-left: 0;
        }
    }
    .sp__blogHeaderWrap .sp__blogHeaderInner .desc__wrap h2{
        color: #323438;
        font-size: 32px;
        line-height: 1.3;
        font-weight: 700;
        text-align: {{ section.settings.text_alignment }};
        margin: 0;
    }
    @media screen and (max-width: 767px){
        .sp__blogHeaderWrap .sp__blogHeaderInner .desc__wrap h2{
            font-size: 24px;
            line-height: 1.2;
        }
    }
    .sp__blogHeaderWrap .sp__blogHeaderInner .btn__wrap{
        width: 200px;
        text-align: left;
    }
    @media screen and (max-width: 767px){
        .sp__blogHeaderWrap .sp__blogHeaderInner .btn__wrap{
            display: none;
        }
    }
    .sp__iconButton{
        direction: rtl;
        display: inline-flex;
        align-items: center;
        padding: 12px 24px;
        border: 1px solid #007A73;
        border-radius: 8px;
        color: #007A73;
    }
    .sp__iconButton .btn__text{
        font-size: 16px;
        line-height: 1.3;
        font-weight: 700;
        margin-left: 4px;
    }
    .sp__iconButton .btn__icon{
        width: 20px;
    }

    .sp__blogFooterWrap{
        display: none;
        padding: 30px 20px 0;
    }
    .sp__blogFooterWrap .btn__wrap{
        text-align: right;
    }
    @media screen and (max-width: 767px){
        .sp__blogFooterWrap{
            display: block;
        }
    }

    .sp__blogArticlesDesktopWrap{
        display: block;
    }
    @media screen and (max-width: 767px){
        .sp__blogArticlesDesktopWrap{
            display: none;
        }
    }

    .sp__blogGridWrapper{
        display: grid;
        direction: rtl;
        padding: 0 120px;
    }

    .sp__blogArticlesMobileWrap{
        display: none;
        padding-right: 20px;
    }
    @media screen and (max-width: 767px){
        .sp__blogArticlesMobileWrap{
            display: block;
        }
    }
</style>

{% schema %}
{
  "name": "SP Blog Posts",
  "settings": [
    { "type": "header", "content": "Content" },
    { "type": "blog", "id": "blog", "label": "Select Blog" },
    { "type": "header", "content": "Section Header" },
    { "type": "text", "id": "heading", "label": "Section Heading" },
    { "type": "text", "id": "button_label", "label": "Button text", "default": "View All" },
    { "type": "url", "id": "button_link", "label": "Button link" },
    { "type": "header", "content": "Blog Settings" },
    { "type": "range", "id": "articles_limit", "label": "Articles to show", "min": 3, "max": 9, "step": 3, "default": 3 },
    { "type": "select", "id": "desktop_columns", "label": "Columns on Desktop", "options": [{"value": "2", "label": "2"},{"value": "3", "label": "3"},{"value": "4", "label": "4"}], "default": "3" },
    { "type": "select", "id": "slider_items", "label": "Slider Items Per View", "options": [{"value": "1", "label": "1"},{"value": "2", "label": "2"}], "default": "1" },
    { "type": "select", "id": "text_alignment", "label": "Text Alignment", "options": [{"value": "left", "label": "Left"},{"value": "center", "label": "Center"},{"value": "right", "label": "Right"}], "default": "center" },
    { "type": "range", "id": "desktop_gap", "label": "Grid Gap Desktop", "min": 10, "max": 50, "step": 1, "default": 30 },
    { "type": "range", "id": "carousel_gap", "label": "Carousel Gap Mobile", "min": 10, "max": 50, "step": 1, "default": 20 },
    { "type": "range", "id": "peak_next", "label": "Peak Next Item", "min": 50, "max": 200, "step": 10, "default": 100 },
    { "type": "header", "content": "Article Card" },
    { "type": "checkbox", "id": "show_date", "label": "Show Date", "default": true },
    { "type": "checkbox", "id": "show_author", "label": "Show Author", "default": false },
    { "type": "checkbox", "id": "show_tags", "label": "Show Tags", "default": true },
    { "type": "checkbox", "id": "show_excerpt", "label": "Show Excerpt", "default": true },
    { "type": "range", "id": "excerpt_length", "label": "Excerpt Length", "min": 50, "max": 250, "step": 10, "default": 100 },
    { "type": "checkbox", "id": "show_read_more", "label": "Show Read More Link", "default": true },
    { "type": "text", "id": "read_more_text", "label": "Read More Link Text", "default": "Read More" }
  ],
  "presets": [{ "name": "SP Blog Posts" }]
}
{% endschema %}

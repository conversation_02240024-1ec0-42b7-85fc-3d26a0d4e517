<section class="sp-offer" style="background-color: {{ section.settings.bg_color }};">
  <div class="sp-offer__container">
    <div class="sp-offer__container-header">
      {% if section.settings.title != blank %}
        <h2
          class="sp-offer__title"
          style="font-size: {{ section.settings.title_size }}px; color: {{ section.settings.title_color }};"
        >
          {{ section.settings.title }}
        </h2>
      {% endif %}
      {% if section.settings.description != blank %}
        <p
          class="sp-offer__description"
          style="font-size: {{ section.settings.desc_size }}px; color: {{ section.settings.desc_color }};"
        >
          {{ section.settings.description }}
        </p>
      {% endif %}
    </div>

    <div class="sp-offer__grid">
      {% assign text_blocks = section.blocks | where: 'type', 'text_column' %}
      {% assign image_blocks = section.blocks | where: 'type', 'image_column' %}

      <div class="offer__grid-column">
        <div class="grid__column grid__columnHalf">
            {% comment %} <div class="grid__item"> {% endcomment %}
                <!-- Image Block 2 -->
                {% assign block = image_blocks[1] %}
                {% if block %}
                <div
                    class="grid__item sp-offer__column sp-offer__column-image"
                    style="background-color: {{ section.settings.card_bg }}; background-image: url({{ block.settings.image | image_url: width: 300 }});"
                    {{ block.shopify_attributes }}
                >
                    {% comment %} {% if block.settings.image %}
                    <div class="sp-offer__image">
                        <img src="{{ block.settings.image | image_url: width: 300 }}" alt="Offer Image" loading="lazy">
                    </div>
                    {% endif %} {% endcomment %}
                </div>
                {% endif %}
            {% comment %} </div> {% endcomment %}
            <div class="grid__item">
                <!-- Text Block 3 -->
                {% assign block = text_blocks[2] %}
                {% if block %}
                <div
                    class="sp-offer__column"
                    style="background-color: {{ section.settings.card_bg }};"
                    {{ block.shopify_attributes }}
                >
                    {% if block.settings.number != blank %}
                    <div class="sp-offer__number" style="color: {{ section.settings.card_title_color }};">
                        {{ block.settings.number }}
                    </div>
                    {% endif %}
                    {% if block.settings.title != blank %}
                    <div
                        class="sp-offer__column-title"
                        style="font-size: {{ section.settings.card_title_size }}px; color: {{ section.settings.card_title_color }};"
                    >
                        {{ block.settings.title }}
                    </div>
                    {% endif %}
                    {% if block.settings.description != blank %}
                    <div
                        class="sp-offer__column-text"
                        style="font-size: {{ section.settings.card_text_size }}px; color: {{ section.settings.card_text_color }};"
                    >
                        {{ block.settings.description }}
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>

        <div class="grid__column">
            <div class="grid__item">
                <!-- Text Block 4 -->
                {% assign block = text_blocks[3] %}
                {% if block %}
                <div
                    class="sp-offer__column"
                    style="background-color: {{ section.settings.card_bg }};"
                    {{ block.shopify_attributes }}
                >
                    {% if block.settings.number != blank %}
                    <div class="sp-offer__number" style="color: {{ section.settings.card_title_color }};">
                        {{ block.settings.number }}
                    </div>
                    {% endif %}
                    {% if block.settings.title != blank %}
                    <div
                        class="sp-offer__column-title"
                        style="font-size: {{ section.settings.card_title_size }}px; color: {{ section.settings.card_title_color }};"
                    >
                        {{ block.settings.title }}
                    </div>
                    {% endif %}
                    {% if block.settings.description != blank %}
                    <div
                        class="sp-offer__column-text"
                        style="font-size: {{ section.settings.card_text_size }}px; color: {{ section.settings.card_text_color }};"
                    >
                        {{ block.settings.description }}
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
      </div>

      <div class="offer__grid-column grid__columnHalf">
        <div class="grid__column">
            {% comment %} <div class="grid__item"> {% endcomment %}
            <!-- Image Block 1 -->
            {% assign block = image_blocks[0] %}
            {% if block %}
                <div
                class="grid__item sp-offer__column sp-offer__column-image"
                style="background-color: {{ section.settings.card_bg }}; background-image: url({{ block.settings.image | image_url: width: 300 }});"
                {{ block.shopify_attributes }}
                >
                {% comment %} {% if block.settings.image %}
                    <div class="sp-offer__image">
                    <img src="{{ block.settings.image | image_url: width: 300 }}" alt="Offer Image" loading="lazy">
                    </div>
                {% endif %} {% endcomment %}
                </div>
            {% endif %}
            {% comment %} </div> {% endcomment %}
        </div>
        <div class="grid__column">
            <div class="grid__item">
                <!-- Block 1 -->
                {% assign block = text_blocks[0] %}
                {% if block %}
                    <div
                    class="sp-offer__column"
                    style="background-color: {{ section.settings.card_bg }};"
                    {{ block.shopify_attributes }}
                    >
                    {% if block.settings.number != blank %}
                        <div class="sp-offer__number" style="color: {{ section.settings.card_title_color }};">
                        {{ block.settings.number }}
                        </div>
                    {% endif %}
                    {% if block.settings.title != blank %}
                        <div
                        class="sp-offer__column-title"
                        style="font-size: {{ section.settings.card_title_size }}px; color: {{ section.settings.card_title_color }};"
                        >
                        {{ block.settings.title }}
                        </div>
                    {% endif %}
                    {% if block.settings.description != blank %}
                        <div
                        class="sp-offer__column-text"
                        style="font-size: {{ section.settings.card_text_size }}px; color: {{ section.settings.card_text_color }};"
                        >
                        {{ block.settings.description }}
                        </div>
                    {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="grid__item">
                <!-- Text Block 2 -->
                {% assign block = text_blocks[1] %}
                {% if block %}
                    <div
                    class="sp-offer__column"
                    style="background-color: {{ section.settings.card_bg }};"
                    {{ block.shopify_attributes }}
                    >
                    {% if block.settings.number != blank %}
                        <div class="sp-offer__number" style="color: {{ section.settings.card_title_color }};">
                        {{ block.settings.number }}
                        </div>
                    {% endif %}
                    {% if block.settings.title != blank %}
                        <div
                        class="sp-offer__column-title"
                        style="font-size: {{ section.settings.card_title_size }}px; color: {{ section.settings.card_title_color }};"
                        >
                        {{ block.settings.title }}
                        </div>
                    {% endif %}
                    {% if block.settings.description != blank %}
                        <div
                        class="sp-offer__column-text"
                        style="font-size: {{ section.settings.card_text_size }}px; color: {{ section.settings.card_text_color }};"
                        >
                        {{ block.settings.description }}
                        </div>
                    {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
.sp-offer {
    padding: 60px 120px;
    direction: rtl;
}
@media screen and (max-width: 767px){
    .sp-offer {
        padding: 40px 20px;
    }
}
.sp-offer__container .sp-offer__container-header {
    text-align: center;
}
.sp-offer__title {
    margin-bottom: 16px;
}
.sp-offer__description {
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.sp-offer__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    align-items: flex-start;
}

.offer__grid-column{
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 24px;
    align-items: flex-start;
    height: 100%;
}

.grid__column{
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 24px;
  height: 100%;
  align-items: flex-start;
}
.grid__columnHalf{
  grid-template-columns: repeat(2, 1fr);
}
.grid__column .grid__item{
  height: 100%;
}

.sp-offer__column {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end;
    gap: 8px;
    min-height: 190px;
    height: 100%;
    padding: 24px;
    border-radius: 8px;
    {% comment %} text-align: center; {% endcomment %}
    background-color: #F8F8F5;
    border: 1px solid #EDEDED;
    overflow: hidden;
    transition: all ease-in-out 0.5s;
}
.sp-offer__column.sp-offer__column-image {
    padding: 0;
    border-radius: 8px;
    text-align: center;
    background-color: #F8F8F5;
    border: 1px solid #EDEDED;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.sp-offer__image img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.sp-offer__number {
    font-size: 42px;
    line-height: 59px;
    font-weight: 400;
    letter-spacing: 0.3px;
    margin: 0;
}

.sp-offer__column-title {
    line-height: 1.63;
    font-weight: 500;
    margin: 0;
}

.sp-offer__column-text {
    line-height: 1.63;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: all ease-in-out 0.5s;
}

.sp-offer__column:hover .sp-offer__column-text{
  max-height: 500px;
}

@media (max-width: 992px) {
    .sp-offer__grid {
        grid-template-columns: repeat(1, 1fr);
    }
}
</style>

{% schema %}
{
  "name": "SP What We Offer",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "What We Offer"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Section Description",
      "default": "Explore the offerings and benefits of joining us."
    },
    {
      "type": "color",
      "id": "bg_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "number",
      "id": "title_size",
      "label": "Title Font Size (px)",
      "default": 32
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title Color",
      "default": "#111111"
    },
    {
      "type": "number",
      "id": "desc_size",
      "label": "Description Font Size (px)",
      "default": 18
    },
    {
      "type": "color",
      "id": "desc_color",
      "label": "Description Color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "card_bg",
      "label": "Card Background",
      "default": "#ffffff"
    },
    {
      "type": "number",
      "id": "card_title_size",
      "label": "Card Title Font Size (px)",
      "default": 20
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card Title Color",
      "default": "#222222"
    },
    {
      "type": "number",
      "id": "card_text_size",
      "label": "Card Text Font Size (px)",
      "default": 16
    },
    {
      "type": "color",
      "id": "card_text_color",
      "label": "Card Text Color",
      "default": "#666666"
    }
  ],
  "blocks": [
    {
      "type": "text_column",
      "name": "Text Column",
      "settings": [
        {
          "type": "text",
          "id": "number",
          "label": "Number",
          "default": "01"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Column Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "Brief description of the column content."
        }
      ]
    },
    {
      "type": "image_column",
      "name": "Image Column",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Column Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "SP What We Offer",
      "category": "Custom",
      "blocks": [
        { "type": "text_column" },
        { "type": "text_column" },
        { "type": "image_column" },
        { "type": "text_column" },
        { "type": "image_column" },
        { "type": "text_column" }
      ]
    }
  ]
}
{% endschema %}

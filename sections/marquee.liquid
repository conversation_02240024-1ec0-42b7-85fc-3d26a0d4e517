

<script
  src="{{ 'marquee.js' | asset_url }}"
  type="module"
></script>

{% assign gap_between_elements = section.settings.gap_between_elements %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<marquee-component
  class="section spacing-style gap-style color-{{ section.settings.color_scheme }}"
  style="
    {%- render 'spacing-style', settings: section.settings %};
    {% render 'gap-style', value: gap_between_elements, name: 'marquee-gap' %}
    --marquee-direction: {{ section.settings.movement_direction }};
  "
  data-speed-factor="20"
  data-movement-direction="{{ section.settings.movement_direction }}"
>
  <div
    class="marquee__wrapper"
    ref="wrapper"
  >
    <div
      class="marquee__content"
      ref="content"
    >
      <div class="marquee__repeated-items">
        {% content_for 'blocks' %}
      </div>
    </div>
  </div>
</marquee-component>

{% stylesheet %}
  marquee-component {
    display: block;
    width: 100%;
    overflow: hidden;
  }

  .marquee__wrapper {
    display: flex;
    gap: var(--marquee-gap);
    width: fit-content;
    white-space: nowrap;
  }

  .marquee__content {
    min-width: max-content;
    display: flex;
    gap: var(--marquee-gap);
  }

  .marquee__content :is(p, h1, h2, h3, h4, h5, h6) {
    white-space: nowrap;
  }

  .marquee__content .marquee__repeated-items * {
    max-width: none;
  }

  .marquee__repeated-items {
    min-width: max-content;
    display: flex;
    gap: var(--marquee-gap);
    align-items: center;
    justify-content: center;
  }

  .marquee__repeated-items > * {
    align-content: center;
  }

  @media (prefers-reduced-motion: no-preference) {
    marquee-component:not([data-disabled]) .marquee__wrapper {
      animation: marquee-motion var(--marquee-speed) linear infinite var(--marquee-direction);
    }
  }

  @keyframes marquee-motion {
    to {
      transform: translate3d(calc(-50% - (var(--marquee-gap) / 2)), 0, 0);
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.marquee",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "blocks": [
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "logo"
    },
    {
      "type": "_divider"
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "movement_direction",
      "label": "t:settings.motion_direction",
      "options": [
        {
          "value": "reverse",
          "label": "t:options.forward"
        },
        {
          "value": "normal",
          "label": "t:options.reverse"
        }
      ],
      "default": "normal"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "gap_between_elements",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    }
  ],
  "presets": [
    {
      "name": "t:names.marquee_section",
      "category": "t:categories.banners",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>We make things that work better and last longer.</p>",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "var(--font-size--h2)",
            "line_height": "tight",
            "letter_spacing": "tight",
            "case": "none",
            "wrap": "nowrap",
            "width": "fit-content"
          }
        }
      },
      "block_order": ["text"]
    }
  ]
}
{% endschema %}

{% assign ai_gen_id = section.id | replace: '_', '' | downcase %}

<review-carousel-{{ ai_gen_id }} class="section section--page-width review-carousel-wrapper-{{ ai_gen_id }}">
  <div class="review-carousel-heading">
    <div class="review-carousel-nav desktop">
      <button class="review-carousel__nav review-carousel__nav--prev-{{ ai_gen_id }}" data-prev>
        {{ 'arrow-fig.svg' | inline_asset_content }}
      </button>
      <button class="review-carousel__nav review-carousel__nav--next-{{ ai_gen_id }}" data-next>
        {{ 'arrow-fig.svg' | inline_asset_content }}
      </button>
    </div>
     <h3 class="review-title" style="text-align: right; font-size:32px; font-weight:bold;">{{ section.settings.heading }}</h3>
  </div>

  <div class="review-carousel__container-{{ ai_gen_id }}">
    <div class="review-carousel__track-{{ ai_gen_id }}" data-track>
      {% for i in (1..8) %}
        {% assign name_key = 'reviewer_name_' | append: i %}
        {% assign text_key = 'review_text_' | append: i %}
        {% assign rating_key = 'review_rating_' | append: i %}

        {% assign name = section.settings[name_key] %}
        {% assign text = section.settings[text_key] %}
        {% assign rating = section.settings[rating_key] %}

        {% if text != blank %}
          <div class="review-carousel__slide-{{ ai_gen_id }}" data-slide="{{ forloop.index0 }}">
            <div class="review-box">
              <div class="review-block">
                {% if rating %}
                  <div class="review-stars">
                    {% for star in (1..rating) %}
                      <span style="color: #FFC942;">★</span>
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="read-more-container">
                <p class="review-text" id="readMoreText">{{ text | truncate: 80, "" }}
                  <span class="more-text" style="display: none;">
                 {{ text | slice: 80, text.size }}
                </span>
                </p>
                <button style="color: #007A73; text-decoration: underline; background: transparent;" class="read-more-button" onclick="toggleReadMore(this)">
                  קראו עוד
                </button>
                {% comment %} <p class="read-more" style="color: #007A73; text-decoration: underline;"> קראו עוד</p> {% endcomment %}
                </div>
                {% if name %}
                  <p class="review-name">{{ name }}</p>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>

  <div class="review-carousel-heading">
    <div class="review-carousel-nav mobile">
      <button class="review-carousel__nav review-carousel__nav--prev-{{ ai_gen_id }}" data-prev>
        {{ 'arrow-fig.svg' | inline_asset_content }}
      </button>
      <button class="review-carousel__nav review-carousel__nav--next-{{ ai_gen_id }}" data-next>
        {{ 'arrow-fig.svg' | inline_asset_content }}
      </button>
    </div>
  </div>
</review-carousel-{{ ai_gen_id }}>

<style>

  
.review-carousel-wrapper-{{ ai_gen_id }} {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
  position: relative;
}
.review-carousel-heading {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 2.5rem;
}
.review-carousel-nav button {
  margin-left: 0.5rem;
}
.review-carousel__container-{{ ai_gen_id }} {
  overflow: hidden;
  position: relative;
}
.review-carousel__track-{{ ai_gen_id }} {
  display: flex;
  gap: 20px;
  transition: transform 0.3s ease;
  will-change: transform;
}
.review-carousel__slide-{{ ai_gen_id }} {
  flex: 0 0 calc((100% - 40px) / 3);
  text-align: center;
}
.review-box {
  background: #fff;
  border-radius: 10px;
  /* box-shadow: 0 4px 8px rgba(0,0,0,0.08); */
  padding: 1rem;
  text-align: right;
    direction: rtl;
}
.review-block {
  padding: 1rem;
}
.review-block .review-stars {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}
.review-block .review-text {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #333;
}
.review-block .review-name {
  font-weight: 700;
  color: #323438;
}
.review-carousel__nav {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  background: #fff;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
/* .review-carousel__nav svg {
  width: 16px;
  height: 16px;
} */
.review-carousel__nav--next-{{ ai_gen_id }} svg {
  transform: rotate(180deg);
}
@media screen and (max-width: 990px) {
  .review-carousel__slide-{{ ai_gen_id }} {
    flex: 0 0 calc((100% - 20px) / 2);
  }
}
@media screen and (max-width: 767px) {
  .review-carousel__slide-{{ ai_gen_id }} {
    flex: 0 0 100%;
  }
  .desktop {
    display: none;
  }
}
  
 @media screen and (min-width: 767px) {
  .mobile {
    display: none;
  }
} 

{% comment %} =====read more button css==== {% endcomment %}
 {% comment %} .read-more-container {
  max-width: 300px;
  line-height: 1.6;
} {% endcomment %}

.read-more-button {
  margin-top: 8px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.read-more-button:hover {
  text-decoration: underline;
}

</style>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const root = document.querySelector('review-carousel-{{ ai_gen_id }}');
    if (!root) return;

    const track = root.querySelector('[data-track]');
    const slides = root.querySelectorAll('[data-slide]');
    const prevBtns = root.querySelectorAll('[data-prev]');
    const nextBtns = root.querySelectorAll('[data-next]');

    let visibleSlides = 3;
    let currentIndex = slides.length - visibleSlides;
    let maxIndex = 0;

    // Drag variables
    let isDragging = false;
    let startX = 0;
    let currentTranslate = 0;
    let prevTranslate = 0;
    let animationID = 0;

    function updateVisibleSlides() {
      const width = window.innerWidth;
      if (width <= 749) {
        visibleSlides = 1;
      } else if (width <= 990) {
        visibleSlides = 2;
      } else {
        visibleSlides = 3;
      }
      maxIndex = Math.max(0, slides.length - visibleSlides);
      currentIndex = maxIndex;
      updateCarousel();
    }

    function updateCarousel() {
      const slideWidth = slides[0].offsetWidth;
      const gap = parseInt(getComputedStyle(track).gap) || 0;
      const offset = -(currentIndex * (slideWidth + gap));
      track.style.transition = 'transform 0.3s ease';
      track.style.transform = `translateX(${offset}px)`;

      prevBtns.forEach(btn => btn.disabled = currentIndex === 0);
nextBtns.forEach(btn => btn.disabled = currentIndex >= maxIndex);
    }

    function goToNext() {
      if (currentIndex < maxIndex) {
        currentIndex++;
        updateCarousel();
      }
    }

    function goToPrev() {
      if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
      }
    }

    prevBtns.forEach(btn => btn.addEventListener('click', goToPrev));
nextBtns.forEach(btn => btn.addEventListener('click', goToNext));
    window.addEventListener('resize', updateVisibleSlides);

    // Drag event handlers
    track.addEventListener('mousedown', startDrag);
    track.addEventListener('touchstart', startDrag, { passive: true });

    track.addEventListener('mousemove', drag);
    track.addEventListener('touchmove', drag, { passive: true });

    track.addEventListener('mouseup', endDrag);
    track.addEventListener('mouseleave', endDrag);
    track.addEventListener('touchend', endDrag);

    function startDrag(e) {
      isDragging = true;
      startX = getPositionX(e);
      track.style.transition = 'none';
      animationID = requestAnimationFrame(animation);
    }

    function drag(e) {
      if (!isDragging) return;
      const currentX = getPositionX(e);
      const deltaX = currentX - startX;
      currentTranslate = prevTranslate + deltaX;
    }

    function endDrag() {
      cancelAnimationFrame(animationID);
      isDragging = false;

      const slideWidth = slides[0].offsetWidth;
      const gap = parseInt(getComputedStyle(track).gap) || 0;
      const threshold = slideWidth / 4;

      const movedBy = currentTranslate - prevTranslate;

      if (movedBy < -threshold && currentIndex < maxIndex) {
        currentIndex++;
      } else if (movedBy > threshold && currentIndex > 0) {
        currentIndex--;
      }

      updateCarousel();
      prevTranslate = -(currentIndex * (slideWidth + gap));
      currentTranslate = prevTranslate;
    }

    function getPositionX(e) {
      return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }

    function animation() {
      if (isDragging) {
        track.style.transform = `translateX(${currentTranslate}px)`;
        requestAnimationFrame(animation);
      }
    }

    updateVisibleSlides();
  });
</script>



{% schema %}
{
  "name": "Review Slider",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Slider Heading",
      "default": "What our customers say"
    },
    {
      "type": "text",
      "id": "reviewer_name_1",
      "label": "Reviewer 1 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_1",
      "label": "Reviewer 1 Text"
    },
    {
      "type": "range",
      "id": "review_rating_1",
      "label": "Reviewer 1 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_2",
      "label": "Reviewer 2 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_2",
      "label": "Reviewer 2 Text"
    },
    {
      "type": "range",
      "id": "review_rating_2",
      "label": "Reviewer 2 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_3",
      "label": "Reviewer 3 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_3",
      "label": "Reviewer 3 Text"
    },
    {
      "type": "range",
      "id": "review_rating_3",
      "label": "Reviewer 3 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_4",
      "label": "Reviewer 4 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_4",
      "label": "Reviewer 4 Text"
    },
    {
      "type": "range",
      "id": "review_rating_4",
      "label": "Reviewer 4 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_5",
      "label": "Reviewer 5 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_5",
      "label": "Reviewer 5 Text"
    },
    {
      "type": "range",
      "id": "review_rating_5",
      "label": "Reviewer 5 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_6",
      "label": "Reviewer 6 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_6",
      "label": "Reviewer 6 Text"
    },
    {
      "type": "range",
      "id": "review_rating_6",
      "label": "Reviewer 6 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_7",
      "label": "Reviewer 7 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_7",
      "label": "Reviewer 7 Text"
    },
    {
      "type": "range",
      "id": "review_rating_7",
      "label": "Reviewer 7 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "text",
      "id": "reviewer_name_8",
      "label": "Reviewer 8 Name"
    },
    {
      "type": "textarea",
      "id": "review_text_8",
      "label": "Reviewer 8 Text"
    },
    {
      "type": "range",
      "id": "review_rating_8",
      "label": "Reviewer 8 Stars",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    }
  ],
  "presets": [
    {
      "name": "Review Slider",
      "category": "Testimonials"
    }
  ]
}
{% endschema %}

<script>
  function toggleReadMore(button) {
    const container = button.closest('.read-more-container');
    const moreText = container.querySelector('.more-text');
    const isHidden = moreText.style.display === 'none';

    if (isHidden) {
      moreText.style.display = 'inline';
      button.textContent = 'קרא פחות';
    } else {
      moreText.style.display = 'none';
      button.textContent = 'קראו עוד';
    }
  }
</script>
{% schema %}
{
  "name": "SP Our Stats Count",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Why Choose Us"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Discover how we provide outstanding service and results."
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button Label",
      "default": "Learn More"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link",
    },
    {
      "type": "color",
      "id": "bg_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_bg",
      "label": "Button Background Color",
      "default": "#111111"
    },
    {
      "type": "color",
      "id": "button_text",
      "label": "Button Text Color",
      "default": "#ffffff"
    }
  ],
  "blocks": [
    {
      "type": "card",
      "name": "Stat Card",
      "settings": [
        {
          "type": "text",
          "id": "number",
          "label": "Number",
          "default": "150+"
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Happy Clients"
        }
      ]
    }
  ],
  "max_blocks": 6,
  "presets": [
    {
      "name": "SP Our Stats Count",
      "category": "Custom",
      "blocks": [
        {
          "type": "card",
          "settings": {
            "number": "150+",
            "label": "Happy Clients"
          }
        },
        {
          "type": "card",
          "settings": {
            "number": "10+",
            "label": "Years Experience"
          }
        }
      ]
    }
  ]
}
{% endschema %}


{% stylesheet %}
.sp-stats-section {
  padding-inline: 120px;
  background-color: {{ section.settings.bg_color }};
}

.sp-stats-section__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  max-width: 100%;
  /* margin: 0 auto; */
  /* align-items: center; */
}

.sp-stats-section__textWrap {
  padding-left: 20%;
}

.sp-stats-section__title {
  color: #323438;
  font-size: 32px;
  line-height: 1.3;
  font-weight: 700;
  margin: 0 0 12px;
}

.sp-stats-section__description {
  color: #323438;
  font-size: 16px;
  line-height: 1.63;
  font-weight: 500;
  margin: 0 0 32px;
}

.sp-stats-section__button {
  padding: 0;
  background-color: {{ section.settings.button_bg }};
  color: {{ section.settings.button_text }};
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  flex-direction: row-reverse;
}
.sp-stats-section__button .icon__wrap {
  background: #FFC942;
  border-radius: 2px;
  padding: 6px 7px;
}
.sp-stats-section__button .icon__wrap svg{
  width: 15px;
  height: auto;
}
.sp-stats-section__button .text__wrap {
  display: inline-block;
  color: #323438;
  font-size: 16px;
  line-height: 1.63;
  font-weight: 500;
}

.sp-stats-section__cards {
  display: flex;
  flex-wrap: wrap;
}
  
.sp-stats-section__card {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #EDEDED;
}

.sp-stats-section__card:not(:first-of-type) {
  padding: 25px 0;
}

.sp-stats-section__card:first-of-type {
  padding-bottom: 25px;
}
  
.sp-stats-section__card:last-child {
  margin-bottom: 0;
}

.sp-stats-section__card-number {
  width: 135px;
}
.sp-stats-section__card-number h3{
  color: #323438;
  font-size: 56px;
  line-height: 1;
  font-weight: 700;
  margin: 0;
}

.sp-stats-section__card-label {
  width: calc(100% - 135px);
  padding-right: 68px;
}
.sp-stats-section__card-label p{
  color: #323438;
  font-size: 16px;
  line-height: 1.63;
  font-weight: 500;
  margin: 0;
}

@media (max-width: 767px) {
  .sp-stats-section__container {
    grid-template-columns: 1fr;
  }
  .sp-stats-section {
    padding-inline: 16px;
  }
  .sp-stats-section__textWrap {
    padding-left: 0;
  }
  .sp-stats-section__description {
    margin: 0 0 24px;
  }
  .sp-stats-section__card-label p {
    font-size: 14px;
  }
  .sp-stats-section__card-number h3 {
    font-size: 48px;
  }
  .sp-stats-section__card-label {
    padding-right: 0;
  }
  .sp-stats-section__card-number {
    width: 115px;
  }
  .sp-stats-section__card {
    justify-content: space-between;
  }
}
{% endstylesheet %}

<section class="sp-stats-section">
  <div class="sp-stats-section__container">
    <div class="sp-stats-section__textWrap">
      {% if section.settings.title != blank %}
        <h2 class="sp-stats-section__title">{{ section.settings.title }}</h2>
      {% endif %}
      {% if section.settings.description != blank %}
        <p class="sp-stats-section__description">{{ section.settings.description }}</p>
      {% endif %}
      {% if section.settings.button_label != blank and section.settings.button_link != blank %}
        <a href="{{ section.settings.button_link }}" class="sp-stats-section__button">
          <span class="icon__wrap">
            <svg width="18" height="8" viewBox="0 0 18 8" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.625 7.125L1.5 4M1.5 4L4.625 0.875M1.5 4H16.5" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          <span class="text__wrap">
            {{ section.settings.button_label }}
          </span>
        </a>
      {% endif %}
    </div>

    <div class="sp-stats-section__cards">
      {% for block in section.blocks %}
        <div class="sp-stats-section__card">
          <div class="sp-stats-section__card-number">
            <h3 class="stat-number" data-original="{{ block.settings.number }}">0</h3>
          </div>
          <div class="sp-stats-section__card-label">
            <p>{{ block.settings.label }}</p>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Four Items Carousel",
  "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title",
      "default": "המומחים סומכים עלינו, גם אתם יכולים"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "לכל המומחים"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color Scheme",
      "default": "scheme-1"
    }
  ],
  "blocks": [
    {
      "type": "carousel_item",
      "name": "Carousel Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "item_image",
          "label": "Item Image"
        },
        {
          "type": "text",
          "id": "item_title",
          "label": "Item Title",
          "default": "כותרת הפריט"
        },
        {
          "type": "text",
          "id": "item_title_secondary",
          "label": "Item Title Secondary",
          "default": "מומחה לביוכימיה"
        },
        {
          "type": "textarea",
          "id": "item_description",
          "label": "Item Description",
          "default": "מומחה מוביל בתחום הרפואה המודרנית והחדשנות הטכנולוגית"
        },
        {
          "type": "url",
          "id": "item_link",
          "label": "Item Link"
        }
      ]
    }
  ],
  "max_blocks": 8,
  "presets": [
    {
      "name": "Four Items Carousel",
      "category": "Custom",
      "blocks": [
        {
          "type": "carousel_item",
          "settings": {
            "item_title": "דוקטור אלון ברזילי",
            "item_title_secondary": "מומחה לביוכימיה",
            "item_description": "מוביל במחקר ביוכימי ופיתוח תוספי תזונה מתקדמים"
          }
        },
        {
          "type": "carousel_item",
          "settings": {
            "item_title": "פרופסור יעל נקש",
            "item_title_secondary": "מומחית לכבד-טכנולוגיות רפואיות",
            "item_description": "חלוצה בטכנולוגיות רפואיות מתקדמות ומחקר כבד"
          }
        },
        {
          "type": "carousel_item",
          "settings": {
            "item_title": "פרופסור דניאל ליבוביץ",
            "item_title_secondary": "ר' המחלקה לחדשנות רפואית",
            "item_description": "מוביל חדשנות רפואית ופיתוח פתרונות טיפוליים חדשניים"
          }
        },
        {
          "type": "carousel_item",
          "settings": {
            "item_title": "דוקטור דנה לוי",
            "item_title_secondary": "חוקרת פרמקולוגיה",
            "item_description": "מתמחה במחקר פרמקולוגי ופיתוח תרופות חדשניות"
          }
        }
      ]
    }
  ]
}
{% endschema %}

{% stylesheet %}
.four-items-carousel {
  padding: 60px 120px;
  background-color: #ffffff;
  direction: rtl;
  overflow: hidden;
}

.four-items-carousel__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60px;
}

.four-items-carousel__title {
  font-size: 2rem;
  font-weight: 700;
  color: #323438;
  text-align: right;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

/* Desktop Layout - 4 items in a row */
.four-items-carousel__container {
  display: flex;
  gap: 16px;
  align-items: stretch;
}

.carousel-item {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 358px;
  display: flex;
  align-items: flex-end;
  min-width: 0;
}

.carousel-item__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.carousel-item__content {
  position: absolute;
  bottom: 8px;
  right: 8px;
  left: 8px;
  width: auto;
  min-height: auto;
  border-radius: 8px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  z-index: 2;
}

.carousel-item__title {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
  color: #323438;
}

.carousel-item__title-secondary {
  font-size: 16px;
  margin: 0 0 16px 0;
  line-height: 1.4;
  font-weight: 500;
  color: #323438;
}

.carousel-item__description {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 1s;
  font-weight: 500;
  color: #323438;
}

/* Desktop hover effects */
@media (min-width: 768px) {
  .carousel-item:hover .carousel-item__description {
    opacity: 1;
    max-height: 200px;
  }
}

.carousel-item__link {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

/* Fallback for no image */
.carousel-item__image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 1rem;
  z-index: 1;
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1024px) {
  .four-items-carousel {
    padding: 50px 60px;
  }
  
  .four-items-carousel__title {
    font-size: 1.75rem;
    margin-bottom: 50px;
  }
  
  .four-items-carousel__container {
    gap: 12px;
  }
  
  .carousel-item__content {
    bottom: 6px;
    right: 6px;
    left: 6px;
    padding: 16px;
    border-radius: 6px;
  }
  
  .carousel-item__title {
    font-size: 16px;
    font-weight: 700;
    margin: 0 0 6px 0;
  }
  
  .carousel-item__title-secondary {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 12px 0;
  }
  
  .carousel-item__description {
    font-size: 14px;
    font-weight: 500;
  }
}

/* Mobile styles - Scrollable carousel */
@media (max-width: 767px) {
  .four-items-carousel {
    padding: 40px 0;
    overflow: visible;
  }
  
  .four-items-carousel__title {
    font-size: 1.5rem;
    margin-bottom: 40px;
    padding: 0 16px;
  }
  
  .four-items-carousel__container {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    padding-left: 16px;
    padding-right: 16px;
    scroll-padding-left: 16px;
    scroll-padding-right: 16px;
    gap: 16px;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .four-items-carousel__container::-webkit-scrollbar {
    display: none;
  }
  
  .carousel-item {
    flex: 0 0 280px;
    scroll-snap-align: start;
    height: 358px;
  }
  
  .carousel-item__content {
    bottom: 7px;
    right: 7px;
    left: 7px;
    padding: 14px;
  }
  
  .carousel-item__title {
    font-size: 16px;
    font-weight: 700;
    margin: 0 0 10px 0;
  }
  
  .carousel-item__title-secondary {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 14px 0;
  }
  
  .carousel-item__description {
    font-size: 14px;
    font-weight: 500;
    opacity: 1;
    max-height: none;
    margin-top: 0;
  }
}

/* RTL specific adjustments */
[dir="rtl"] .four-items-carousel__container {
  direction: rtl;
}

/* Button styles */
.four-items-carousel__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: transparent;
  color: #007A73;
  text-decoration: none;
  border: 1px solid #007A73;
  border-radius: 8px;
  font-weight: 700;
  font-size: 16px;
  transition: all 0.3s ease;
  margin-top: 40px;
}

.four-items-carousel__button:hover {
  background-color: #007A73;
  color: white;
}

.four-items-carousel__button .arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.four-items-carousel__button:hover .arrow-icon {
  transform: translateX(-2px);
}

.four-items-carousel__button-wrapper {
  display: flex;
  justify-content: center;
}

.four-items-carousel__button-wrapper--header {
  margin: 0;
  padding: 0;
}

.four-items-carousel__button-wrapper--mobile {
  text-align: center;
  display: none;
  padding: 0 16px;
  margin-top: 32px;
}

.four-items-carousel__button {
  margin: 0;
  width: auto;
  max-width: none;
}

/* Desktop - button in header */
@media (min-width: 768px) {
  .four-items-carousel__button-wrapper--header {
    display: flex;
  }
  
  .four-items-carousel__button-wrapper--mobile {
    display: none;
  }
}

/* Mobile - button under carousel, hide header button */
@media (max-width: 767px) {
  .four-items-carousel__header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 40px;
  }
  
  .four-items-carousel__title {
    font-size: 1.5rem;
    margin-bottom: 0;
  }
  
  .four-items-carousel__button-wrapper--header {
    display: none;
  }
  
  .four-items-carousel__button-wrapper--mobile {
    display: block;
    text-align: right;
  }
  
  .four-items-carousel__button-wrapper--mobile .four-items-carousel__button {
    width: auto;
    max-width: none;
  }
}

/* Carousel navigation styles */
.four-items-carousel__navigation {
  display: none;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding: 0 16px;
}

.carousel-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #323438;
  padding: 0;
}

.carousel-nav-button:hover {
  color: #323438;
}

.carousel-nav-button:disabled {
  color: #A5A8AD;
  cursor: not-allowed;
}

.carousel-nav-button:disabled:hover {
  color: #A5A8AD;
}

.carousel-nav-arrows {
  display: flex;
  align-items: center;
  gap: 8px;
}

.carousel-progress {
  flex: 1;
  height: 2px;
  background: #D0D1D5;
  overflow: hidden;
  position: relative;
}

.carousel-progress-bar {
  height: 100%;
  background: #616266;
  transition: width 0.3s ease;
  width: 25%;
}

/* Mobile - show navigation */
@media (max-width: 767px) {
  .four-items-carousel__navigation {
    display: flex;
  }
}

/* Smooth scrolling for mobile */
@media (max-width: 767px) {
  .four-items-carousel__container {
    scroll-behavior: smooth;
  }
}
{% endstylesheet %}

<section class="four-items-carousel color-{{ section.settings.color_scheme }}"{% if section.settings.color_scheme %} data-color-scheme="{{ section.settings.color_scheme }}"{% endif %}>
  <div class="four-items-carousel__header">
    {% if section.settings.section_title != blank %}
      <h2 class="four-items-carousel__title">{{ section.settings.section_title }}</h2>
    {% endif %}
    
    {% if section.settings.button_text != blank %}
      <div class="four-items-carousel__button-wrapper four-items-carousel__button-wrapper--header">
        <a href="{{ section.settings.button_link | default: '/pages/our-scientists' }}" class="four-items-carousel__button">
          {{ section.settings.button_text }}
          <span class="arrow-icon">
            <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </span>
        </a>
      </div>
    {% endif %}
  </div>
  
  <div class="four-items-carousel__container">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'carousel_item' %}
          <div class="carousel-item" {{ block.shopify_attributes }}>
            {% if block.settings.item_link != blank %}
              <a href="{{ block.settings.item_link }}" class="carousel-item__link">
            {% endif %}
            
            {% if block.settings.item_image != blank %}
              <img 
                src="{{ block.settings.item_image | img_url: '600x600' }}" 
                alt="{{ block.settings.item_title | escape }}"
                class="carousel-item__image"
                loading="lazy"
              >
            {% else %}
              <div class="carousel-item__image-placeholder">
                <span>תמונת מומחה</span>
              </div>
            {% endif %}
            
            <div class="carousel-item__content">
              {% if block.settings.item_title != blank %}
                <h3 class="carousel-item__title">{{ block.settings.item_title }}</h3>
              {% endif %}
              
              {% if block.settings.item_title_secondary != blank %}
                <p class="carousel-item__title-secondary">{{ block.settings.item_title_secondary }}</p>
              {% endif %}
              
              {% if block.settings.item_description != blank %}
                <p class="carousel-item__description">{{ block.settings.item_description }}</p>
              {% endif %}
            </div>
            
            {% if block.settings.item_link != blank %}
              </a>
            {% endif %}
          </div>
      {% endcase %}
    {% endfor %}
  </div>
  
  <!-- Mobile Navigation -->
  <div class="four-items-carousel__navigation">
    <div class="carousel-progress">
      <div class="carousel-progress-bar"></div>
    </div>
    
    <div class="carousel-nav-arrows">
      <button class="carousel-nav-button carousel-nav-prev" onclick="navigateCarousel('prev')">
        <svg width="15" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg" style="transform: rotate(180deg);">
          <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </button>
      
      <button class="carousel-nav-button carousel-nav-next" onclick="navigateCarousel('next')">
        <svg width="15" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </button>
    </div>
  </div>
  
  {% if section.settings.button_text != blank %}
    <div class="four-items-carousel__button-wrapper four-items-carousel__button-wrapper--mobile">
      <a href="{{ section.settings.button_link | default: '/pages/our-scientists' }}" class="four-items-carousel__button">
        {{ section.settings.button_text }}
        <span class="arrow-icon">
          <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </span>
      </a>
    </div>
  {% endif %}
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const carousel = document.querySelector('.four-items-carousel__container');
  const progressBar = document.querySelector('.carousel-progress-bar');
  const prevButton = document.querySelector('.carousel-nav-prev');
  const nextButton = document.querySelector('.carousel-nav-next');
  const items = document.querySelectorAll('.carousel-item');
  
  if (!carousel || !progressBar || !prevButton || !nextButton || items.length === 0) return;
  
  let currentIndex = 0;
  const totalItems = items.length;
  let isScrolling = false;
  
  function updateProgress() {
    const progress = ((currentIndex + 1) / totalItems) * 100;
    progressBar.style.width = progress + '%';
  }
  
  function updateButtons() {
    prevButton.disabled = currentIndex === 0;
    nextButton.disabled = currentIndex === totalItems - 1;
  }
  
  function scrollToItem(index) {
    if (index >= 0 && index < totalItems) {
      isScrolling = true;
      const item = items[index];
      const scrollLeft = item.offsetLeft - 16; // Account for padding
      
      carousel.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });
      
      currentIndex = index;
      updateProgress();
      updateButtons();
      
      // Reset scrolling flag after animation
      setTimeout(() => {
        isScrolling = false;
      }, 500);
    }
  }
  
  // Navigation functions
  window.navigateCarousel = function(direction) {
    if (isScrolling) return; // Prevent rapid clicking
    
    if (direction === 'prev' && currentIndex > 0) {
      scrollToItem(currentIndex - 1);
    } else if (direction === 'next' && currentIndex < totalItems - 1) {
      scrollToItem(currentIndex + 1);
    }
  };
  
  // Update on scroll (debounced)
  let scrollTimeout;
  carousel.addEventListener('scroll', function() {
    if (isScrolling) return; // Don't update during programmatic scroll
    
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      const scrollLeft = carousel.scrollLeft + 16; // Account for padding
      let newIndex = 0;
      let minDistance = Infinity;
      
      // Find the item closest to the center of the viewport
      const viewportCenter = scrollLeft + (carousel.offsetWidth / 2);
      
      for (let i = 0; i < totalItems; i++) {
        const item = items[i];
        const itemCenter = item.offsetLeft + (item.offsetWidth / 2);
        const distance = Math.abs(viewportCenter - itemCenter);
        
        if (distance < minDistance) {
          minDistance = distance;
          newIndex = i;
        }
      }
      
      if (newIndex !== currentIndex) {
        currentIndex = newIndex;
        updateProgress();
        updateButtons();
      }
    }, 100);
  });
  
  // Initialize
  updateProgress();
  updateButtons();
});
</script>

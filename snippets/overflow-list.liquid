{%- doc -%}
  @param {string} children - The children of the overflow list.
  @param {string} [class] - The class that is applied on the overflow-list element.
  @param {boolean} [defer] - Whether to defer the loading of the overflow list.
  @param {number} [minimum-items] - The minimum number of items to show in the overflow list.
  @param {string} [more-attributes] - The attributes that are applied on the more button.
  @param {string} [ref] - The ref that is set on the overflow-list element.
{%- enddoc -%}

<overflow-list
  {% if ref %}
    ref="{{ ref }}"
  {% endif %}
  {% if class %}
    class="{{ class }}"
  {% endif %}
  {% if minimum-items %}
    minimum-items="{{ minimum-items }}"
  {% endif %}
  {% if defer %}
    defer
  {% endif %}
>
  <template shadowrootmode="open">
    {{ 'overflow-list.css' | asset_url | stylesheet_tag }}

    <ul part="list">
      <slot></slot>
      <slot
        name="more"
        part="more"
        hidden
        {% if more-attributes %}
          {{ more-attributes }}
        {% endif %}
      >
        <li
          part="more"
        >
          <button
            class="button"
            type="button"
            tabindex="0"
          >
            {{ 'actions.more' | t }}
          </button>
        </li>
      </slot>
      <li
        part="placeholder"
        hidden
      ></li>
    </ul>

    <div part="overflow">
      <ul part="overflow-list">
        <slot name="overflow"></slot>
      </ul>
    </div>
  </template>

  {{ children }}
</overflow-list>

<script
  type="module"
  src="{{ 'cart-note.js' | asset_url }}"
></script>

<cart-note style="display: flex;">
  <accordion-custom class="cart-note">
    <details
      class="details"
    >
      <summary class="cart-note__summary">
        <span class="cart-note__label h6">
          {{ 'content.seller_note' | t }}
        </span>

        <span class="svg-wrapper icon-plus">
          {{- 'icon-plus.svg' | inline_asset_content -}}
        </span>
      </summary>

      <div class="details-content cart-note__inner">
        <label
          for="cart-note"
          class="visually-hidden"
        >
          {{- 'content.seller_note' | t -}}
        </label>
        <textarea
          form="cart-form"
          on:input="/updateCartNote"
          id="cart-note"
          class="cart-note__instructions"
          name="note"
        >{{ cart.note }}</textarea>
      </div>
    </details>
  </accordion-custom>
</cart-note>

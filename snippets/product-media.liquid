{%- doc -%}
  Renders a product media component.

  @param {object} media - The product media object.
  @param {boolean} [preview_image_only] - Renders only the preview image without controls.
  @param {string} [widths] - Image widths for responsive images.
  @param {string} [sizes] - Image sizes for responsive images.
  @param {string} [loading] - The loading attribute for the image.
  @param {object} [block] - The block object.
  @param {object} [section] - The section object.
  @param {object} [selected_product] - The currently selected product.
  @param {boolean} [first_3d_model] - Indicates if this is the first 3D model.

  @example
  {% render 'product-media', media: media, preview_image_only: false, loading: 'lazy' %}
{%- enddoc -%}

{% liquid
  assign widths = widths | default: '300, 450, 600, 750, 900, 1050, 1200, 1350, 1500, 1650, 1800, 1950, 2000, 2500, 3000, 3500, 4000, 5000'
%}

<div
  class="product-media"
  style="--ratio: {{ media.aspect_ratio }}"
  data-media-id="{{ media.id }}"
>
  {% liquid
    assign high_res_url = media.preview_image | image_url: width: 3840
  %}
  {{
    media.preview_image
    | image_url: width: 1946
    | image_tag:
      widths: widths,
      alt: media.alt,
      sizes: sizes,
      loading: loading,
      class: 'product-media__image',
      transitionToProduct: settings.transition_to_main_product,
      data_max_resolution: high_res_url
  }}

  {% unless preview_image_only %}
    {%- case media.media_type -%}
      {% when 'model' %}
        <product-model
          class="product-model-wrapper"
          data-media-id="{{ media.id }}"
        >
          <button
            class="button deferred-media__poster-button button-unstyled"
            ref="deferredMediaPlayButton"
            on:click="/showDeferredMedia"
          >
            {{
              media.preview_image
              | image_url: width: 1946
              | image_tag:
                widths: widths,
                alt: media.alt,
                sizes: sizes,
                loading: loading,
                class: 'deferred-media__poster-image'
            }}
            <span class="deferred-media__poster-icon">
              <span class="visually-hidden">{{ 'accessibility.play_model' | t }}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
                focusable="false"
                class="icon icon-3d-model"
                fill="none"
                viewBox="0 0 18 21"
              >
                {% render 'icon', icon: '3d-model' %}
              </svg>
            </span>
          </button>

          <template>
            {% # Should I be using media_tag here instead like on Dawn? 🤔 %}
            {{ media | model_viewer_tag }}
          </template>
        </product-model>
        <button
          class="button button-secondary button-shopify-xr"
          type="button"
          data-shopify-xr
          data-shopify-model3d-id="{{ media.id }}"
          data-shopify-title="{{ selected_product.title | escape }}"
          data-shopify-xr-hidden
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
            focusable="false"
            class="icon icon-3d-model"
            fill="none"
            viewBox="0 0 18 21"
          >
            {% render 'icon', icon: '3d-model' %}
          </svg>
          {{ 'actions.view_in_your_space' | t }}
        </button>

        {%- if first_3d_model -%}
          <script
            type="application/json"
            id="ProductModelJSON-{{ selected_product.id }}"
          >
            {{ selected_product.media | where: 'media_type', 'model' | json }}
          </script>
        {%- endif -%}
      {% when 'video', 'external_video' %}
        {%- render 'video',
          video: media,
          video_loop: block.settings.video_loop,
          widths: widths,
          sizes: sizes,
          loading: loading,
          disable_controls: true,
          section_id: section.id
        -%}
    {% endcase %}
  {% endunless %}
</div>

{% stylesheet %}
  .product-media {
    aspect-ratio: var(--gallery-aspect-ratio, var(--ratio));
    min-height: 0;
    min-width: 0;
  }

  /*** Media border-radius feature ****/
  @media screen and (width >= 750px) {
    .media-gallery--carousel slideshow-container,
    .media-gallery--grid .product-media > * {
      border-radius: var(--media-radius, 0);
      overflow: hidden;
    }

    /* When the CAROUSEL is on the LEFT side */
    .product-information:not(.product-information--media-right)
      .media-gallery--carousel.media-gallery--extend
      slideshow-container {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    /* When the CAROUSEL is on the RIGHT side */
    .product-information.product-information--media-right
      .media-gallery--carousel.media-gallery--extend
      slideshow-container {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    /* When the GRID is on the LEFT side */
    .product-information:not(.product-information--media-right) {
      /* One column */
      .media-gallery--grid.media-gallery--extend:not(.media-gallery--two-column) .product-media > *,
      /* Two column, small first image */
      .media-gallery--grid.media-gallery--extend.media-gallery--two-column:not(.media-gallery--large-first-image)
        .product-media-container:nth-of-type(odd)
        .product-media
        > *,
      /* Two column, large first image */
      .media-gallery--grid.media-gallery--extend.media-gallery--two-column.media-gallery--large-first-image
        .product-media-container:is(:first-of-type, :nth-of-type(even))
        .product-media
        > * {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }

    /* When the GRID is on the RIGHT side */
    .product-information.product-information--media-right {
      /* One column */
      .media-gallery--grid.media-gallery--extend:not(.media-gallery--two-column) .product-media > *,
      /* Two column, small first image */
      .media-gallery--grid.media-gallery--extend.media-gallery--two-column:not(.media-gallery--large-first-image)
        .product-media-container:nth-of-type(even)
        .product-media
        > *,
      /* Two column, large first image */
      .media-gallery--grid.media-gallery--extend.media-gallery--two-column.media-gallery--large-first-image
        .product-media-container:is(:first-of-type, :nth-of-type(odd))
        .product-media
        > * {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  ::view-transition-old(gallery-item),
  ::view-transition-new(gallery-item) {
    animation-duration: 0ms;
  }
{% endstylesheet %}

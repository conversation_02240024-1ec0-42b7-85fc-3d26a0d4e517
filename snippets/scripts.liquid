<script type="importmap">
  {
    "imports": {
      "@theme/product-title": "{{ 'product-title-truncation.js' | asset_url }}",
      "@theme/component": "{{ 'component.js' | asset_url }}",
      "@theme/dialog": "{{ 'dialog.js' | asset_url }}",
      "@theme/events": "{{ 'events.js' | asset_url }}",
      "@theme/focus": "{{ 'focus.js' | asset_url }}",
      "@theme/morph": "{{ 'morph.js' | asset_url }}",
      "@theme/paginated-list": "{{ 'paginated-list.js' | asset_url }}",
      "@theme/performance": "{{ 'performance.js' | asset_url }}",
      "@theme/product-form": "{{ 'product-form.js' | asset_url }}",
      "@theme/recently-viewed-products": "{{ 'recently-viewed-products.js' | asset_url }}",
      "@theme/scrolling": "{{ 'scrolling.js' | asset_url }}",
      "@theme/section-renderer": "{{ 'section-renderer.js' | asset_url }}",
      "@theme/utilities": "{{ 'utilities.js' | asset_url }}",
      "@theme/variant-picker": "{{ 'variant-picker.js' | asset_url }}",
      "@theme/media-gallery": "{{ 'media-gallery.js' | asset_url }}",
      "@theme/quick-add": "{{ 'quick-add.js' | asset_url }}"
    }
  }
</script>

{% if settings.transition_to_main_product %}
  {% # theme-check-disable ParserBlockingScript %}
  <script
    src="{{ 'view-transitions.js' | asset_url }}"
  ></script>
  {% # theme-check-enable %}
{% endif %}

<link
  rel="modulepreload"
  href="{{ 'utilities.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'component.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'section-renderer.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'morph.js' | asset_url }}"
>

{% if template.name == 'collection' or template.name == 'search' %}
  <link
    rel="modulepreload"
    href="{{ 'paginated-list.js' | asset_url }}"
  >

  <link
    rel="modulepreload"
    href="{{ 'product-title-truncation.js' | asset_url }}"
  >
{% endif %}

<link
  rel="modulepreload"
  href="{{ 'focus.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'recently-viewed-products.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'scrolling.js' | asset_url }}"
>
<link
  rel="modulepreload"
  href="{{ 'events.js' | asset_url }}"
>
<script
  src="{{ 'quick-add.js' | asset_url }}"
  type="module"
></script>
{% if settings.show_add_discount_code %}
  <script
    src="{{ 'cart-discount.js' | asset_url }}"
    type="module"
  ></script>
{% endif %}

<script>
  (function() {
    'use strict';

    function applyDiscount() {
      var input = document.getElementById('discount-code');
      if (input && input.value && input.value.trim()) {
        window.location.href = '/checkout?discount=' + encodeURIComponent(input.value.trim());
      }
    }

    function updateButtonState() {
      var input = document.getElementById('discount-code');
      var button = document.getElementById('discount-button');

      if (!input || !button) return false;

      var hasValue = input.value && input.value.trim().length > 0;
      button.disabled = !hasValue;

      if (hasValue) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }

      return true;
    }

    function initDiscountHandlers() {
      var input = document.getElementById('discount-code');
      var button = document.getElementById('discount-button');

      if (!input || !button) return false;

      if (input._discountInitialized) return true;

      input.addEventListener('input', updateButtonState);
      input.addEventListener('keyup', updateButtonState);
      input.addEventListener('change', updateButtonState);
      input.addEventListener('paste', function() {
        setTimeout(updateButtonState, 10);
      });

      input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !button.disabled) {
          e.preventDefault();
          applyDiscount();
        }
      });

      input.addEventListener('textInput', updateButtonState);
      input.addEventListener('compositionend', updateButtonState);

      button.addEventListener('click', function(e) {
        e.preventDefault();
        if (!button.disabled) {
          applyDiscount();
        }
      });

      input._discountInitialized = true;

      updateButtonState();

      console.log('Discount form initialized successfully');
      return true;
    }

    function findAndInitForm() {
      if (initDiscountHandlers()) {
        return true;
      }
      return false;
    }

    window.applyDiscount = applyDiscount;
    window.updateDiscountButton = updateButtonState;
    window.initDiscountForm = findAndInitForm;

    var initAttempts = 0;
    var maxAttempts = 50;

    function tryInit() {
      initAttempts++;

      if (findAndInitForm()) {
        console.log('Discount form found and initialized on attempt', initAttempts);
        return;
      }

      if (initAttempts < maxAttempts) {
        var delay = Math.min(initAttempts * 200, 3000);
        setTimeout(tryInit, delay);
      }
    }

    tryInit();

    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(tryInit, 100);
      setTimeout(tryInit, 500);
      setTimeout(tryInit, 1000);
    });

    window.addEventListener('load', function() {
      setTimeout(tryInit, 100);
      setTimeout(tryInit, 500);
      setTimeout(tryInit, 1000);
      setTimeout(tryInit, 2000);
    });

    document.addEventListener('cart:update', function() {
      setTimeout(function() {
        var input = document.getElementById('discount-code');
        if (input) {
          input._discountInitialized = false;
        }
        tryInit();
      }, 200);
    });

    document.addEventListener('discount:update', function() {
      setTimeout(function() {
        var input = document.getElementById('discount-code');
        if (input) {
          input._discountInitialized = false;
        }
        tryInit();
      }, 200);
    });

    if (typeof MutationObserver !== 'undefined') {
      var observer = new MutationObserver(function(mutations) {
        var shouldTryInit = false;

        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
              if (node.nodeType === 1) {
                if (node.id === 'discount-code' || node.id === 'discount-button' ||
                    (node.querySelector && (node.querySelector('#discount-code') || node.querySelector('#discount-button')))) {
                  shouldTryInit = true;
                }
              }
            });
          }
        });

        if (shouldTryInit) {
          setTimeout(tryInit, 100);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    var periodicCheck = setInterval(function() {
      var input = document.getElementById('discount-code');
      var button = document.getElementById('discount-button');

      if (input && button && !input._discountInitialized) {
        if (findAndInitForm()) {
          clearInterval(periodicCheck);
          setInterval(function() {
            var input = document.getElementById('discount-code');
            if (input && !input._discountInitialized) {
              findAndInitForm();
            }
          }, 5000);
        }
      }
    }, 1000);

  })();
</script>
<script
  src="{{ 'dialog.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'variant-picker.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'product-card.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'product-form.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'accordion-custom.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'media.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'product-price.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'product-title-truncation.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'product-inventory.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'show-more.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'slideshow.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'floating-panel.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'video-background.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'component-quantity-selector.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'media-gallery.js' | asset_url }}"
  type="module"
></script>
<script
  src="{{ 'rte-formatter.js' | asset_url }}"
  type="module"
></script>

{% if localization.available_countries.size > 1 or localization.available_languages.size > 1 %}
  <script
    src="{{ 'localization.js' | asset_url }}"
    type="module"
  ></script>
{% endif %}

{% if template == 'product' %}
  <script type="module">
    import { RecentlyViewed } from '@theme/recently-viewed-products';
    RecentlyViewed.addProduct('{{ product.id }}');
  </script>
{% endif %}

{% if settings.transition_to_main_product %}
  <script
    src="{{ 'product-card-link.js' | asset_url }}"
    type="module"
  ></script>
{% endif %}

<script
  src="{{ 'auto-close-details.js' | asset_url }}"
  defer="defer"
></script>

<script
  defer
  src="{{ 'placeholder-image.js' | asset_url }}"
  type="module"
></script>

<script>
  const basePath = 'https://cdn.shopify.com/static/themes/horizon/placeholders';
  const Theme = {
    placeholders: {
      general: [
        `${basePath}/general-1.png`,
        `${basePath}/general-2.png`,
        `${basePath}/general-3.png`,
        `${basePath}/general-4.png`,
        `${basePath}/general-5.png`,
        `${basePath}/general-6.png`,
        `${basePath}/general-7.png`,
      ],
      product: [`${basePath}/product-ball.png`, `${basePath}/product-cone.png`, `${basePath}/product-cube.png`],
    },
    translations: {
      placeholder_image: `{{ 'content.placeholder_image' | t }}`,
      added: `{{ 'actions.added' | t }}`,
    },
    routes: {
      root: '/',
      cart_add_url: '{{ routes.cart_add_url }}',
      cart_change_url: '{{ routes.cart_change_url }}',
      cart_update_url: '{{ routes.cart_update_url }}',
      cart_url: '{{ routes.cart_url }}',
      predictive_search_url: '{{ routes.predictive_search_url }}',
      search_url: '{{ routes.search_url }}',
    },
    template: {
      name: '{{ template }}',
    },
  };
</script>

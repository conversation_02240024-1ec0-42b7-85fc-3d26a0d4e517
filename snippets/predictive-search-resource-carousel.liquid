{%- doc -%}
  Renders a carousel of predictive search results cards.

  @param {string} title - The title of the carousel.
  @param {object} resources - The resources to display.
  @param {string} resource_type - The type of resource to display.
{%- enddoc -%}

{% liquid
  capture slides
    for resource in resources
      capture children
        render 'resource-card', resource_type: resource_type, resource: resource, image_aspect_ratio: '4 / 5', collection_thumbnails: 'multiple'
      endcapture
      render 'slideshow-slide', index: forloop.index0, children: children, class: 'predictive-search-results__card'
    endfor
  endcapture
%}

{% capture header %}
  <div class="predictive-search-results__resource-header">
    <span
      id="predictive-search-collections"
      class="predictive-search-results__title"
    >
      {{ title }}
    </span>

    {% if resources.size >= 4 %}
      {%- render 'slideshow-controls',
        show_arrows: true,
        icon_style: 'chevron',
        shape: 'none'
      -%}
    {% endif %}
  </div>
{% endcapture %}

{% assign slideshow_class = 'predictive-search-results__list predictive-search-results__wrapper list-unstyled slideshow--single-media' %}
{% if resources.size >= 4 %}
  {% assign slideshow_class = 'predictive-search-results__list predictive-search-results__wrapper list-unstyled' %}
{% endif %}

{% render 'slideshow',
  class: slideshow_class,
  header: header,
  infinite: false,
  slides: slides,
  slide_count: resources.size,
  icon_style: 'chevron',
  slideshow_gutters: 'start end'
%}

{% stylesheet %}
  .predictive-search-results__wrapper slideshow-slides {
    /* Add padding to prevent hover animations from being clipped in slideshow
       15px accommodates:
       - Scale effect (9px on each side from 1.03 scale)
       - Lift effect (4px upward movement)
       - Shadow (15px spread with -5px offset)
       Using 16px for better alignment with our spacing scale */
    padding: 16px;
    margin-block: -16px;
  }
{% endstylesheet %}

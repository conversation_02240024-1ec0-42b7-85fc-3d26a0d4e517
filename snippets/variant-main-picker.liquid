{%- doc -%}
  Renders a default variant picker, used to display the variant picker in the variants block.

  @param {object} product_resource - The product object.
{%- enddoc -%}

{% unless product_resource.has_only_default_variant %}
  {% liquid
    assign button_background_brightness = section.settings.color_scheme.settings.foreground | color_brightness
    if button_background_brightness < 105
      assign strikethrough_color_mix = '#000'
    else
      assign strikethrough_color_mix = '#fff'
    endif
  %}
  <variant-picker
    class="variant-picker spacing-style variant-picker--{{ block.settings.alignment }}"
    style="--color-strikethrough-mix: {{ strikethrough_color_mix }}; {% render 'spacing-style', settings: block.settings %}"
    data-section-id="{{ section.id }}"
    data-product-id="{{ product_resource.id }}"
    data-block-id="{{ block.id }}"
    data-product-url="{{ product_resource.url }}"
    ref="mainVariantPicker"
    {% if product.id == product_resource.id %}
      data-template-product-match="true"
    {% endif %}
    {{ block.shopify_attributes }}
    {% if request.visual_preview_mode %}
      data-shopify-visual-preview
    {% endif %}
  >
    <form class="variant-picker__form">
      {%- for product_option in product_resource.options_with_values -%}
        {%- liquid
          assign swatch_count = product_option.values | map: 'swatch' | compact | size
          assign variant_style = block.settings.variant_style

          if swatch_count > 0 and block.settings.show_swatches
            if block.settings.variant_style == 'dropdown'
              assign variant_style = 'swatch_dropdown'
            else
              assign variant_style = 'swatch'
            endif
          endif

          if variant_style == 'buttons' and settings.variant_button_width == 'equal-width-buttons'
            assign fieldset_id = section.id | append: '-' | append: product_resource.id | append: '-' | append: product_option.name | handleize
            assign option_id_attribute = 'data-option-id="' | append: fieldset_id | append: '"'
            assign longest_value = 0
          endif
        -%}

        {%- if variant_style == 'swatch' or block.settings.variant_style == 'buttons' -%}
          <fieldset
            class="variant-option variant-option--buttons{% if variant_style == 'swatch' %} variant-option--swatches{% else %} variant-option--{{ settings.variant_button_width }}{% endif %}"
            {{ option_id_attribute }}
          >
            <legend>
              {{ product_option.name | escape -}}
              {%- if variant_style == 'swatch' -%}
                <span class="variant-option__swatch-value">{{ product_option.selected_value }}</span>
              {%- endif %}
            </legend>
            {%- for product_option_value in product_option.values -%}
              {% if product_option_value.size > longest_value and option_id_attribute %}
                {% assign longest_value = product_option_value.size %}
              {% endif %}
              <label class="variant-option__button-label{% if variant_style == 'swatch' %} variant-option__button-label--has-swatch{% endif %}">
                <input
                  type="radio"
                  name="{{ product_option.name | escape }}-{{ block.id }}-{{ product_resource.id }}"
                  value="{{ product_option_value | escape }}"
                  aria-label="{{ product_option_value.name }}"
                  {% if product_option_value.available == false %}
                    aria-disabled="true"
                  {% endif %}
                  data-input-id="{{ product_option.position }}-{{ forloop.index0 }}"
                  data-option-value-id="{{ product_option_value.id }}"
                  data-option-available="{{ product_option_value.available }}"
                  data-connected-product-url="{{ product_option_value.product_url }}"
                  {% if product_option_value.variant.id %}
                    data-variant-id="{{ product_option_value.variant.id }}"
                  {% endif %}
                  {% if product_option_value.selected %}
                    checked
                  {% endif %}
                >
                {% if variant_style == 'swatch' %}
                  {% liquid
                    assign featured_media = product_option_value.variant.featured_media

                    # If the variant has no featured media, and we have a combined listing product, then fall back to
                    # using the featured media of the child product that is linked to this option value.
                    if featured_media == blank and product_option_value.product_url
                      assign featured_media = product_option_value.variant.product.featured_media
                    endif
                  %}

                  {% render 'swatch',
                    swatch: product_option_value.swatch,
                    variant_image: featured_media,
                    mode: 'unscaled'
                  %}
                {% else %}
                  <span class="variant-option__button-label__text">{{ product_option_value | escape }}</span>
                {% endif %}
                {% render 'strikethrough-variant', product_option: product_option_value %}
              </label>
            {%- endfor -%}
            {% if option_id_attribute %}
              {% style %}
                [data-option-id="{{ fieldset_id }}"] {
                  --variant-ch: {{ longest_value }}ch;
                }
              {% endstyle %}
            {% endif %}
          </fieldset>
        {%- elsif block.settings.variant_style == 'dropdowns' -%}
          {%
            # There is an opportunity to build a custom select component that will allow us to style the select element further (animation for dropdown, swatches shown in the dropdown options, etc)
            # It's too bad as it mean rebuilding baked in behaviours but I think we've already done that for the locale selectors
            # in dawn. So it might mean more time spent in setting it up but worth it for future updates/styling.
          %}
          {% liquid
            assign property_being_updated = false
            if settings.variant_swatch_width != settings.variant_swatch_height
              assign property_being_updated = true
              # (original width / original height) x new height (20px at the moment) = new width
              assign new_width = settings.variant_swatch_width | times: 1.0 | divided_by: settings.variant_swatch_height | times: 20
            endif
          %}

          <div class="variant-option variant-option--dropdowns">
            <label for="Option-{{ block.id }}-{{ forloop.index0 }}">{{ product_option.name | escape }}</label>
            <div
              class="variant-option__select-wrapper"
              style="
                {%- if property_being_updated  -%}
                  --variant-picker-swatch-width: clamp(10px,{{ new_width }}px, 50px);
                {%- endif -%}
              "
            >
              <select
                id="Option-{{ block.id }}-{{ forloop.index0 }}"
                name="options[{{ product_option.name | escape }}]"
                class="variant-option__select"
              >
                {%- for product_option_value in product_option.values -%}
                  <option
                    value="{{ product_option_value | escape }}"
                    data-input-id="{{ product_option.position }}-{{ forloop.index0 }}"
                    data-option-value-id="{{ product_option_value.id }}"
                    data-variant-id="{{ product_option_value.variant.id }}"
                    data-connected-product-url="{{ product_option_value.product_url }}"
                    {% if product_option_value.selected %}
                      selected="selected"
                    {% endif %}
                  >
                    {% if product_option_value.available == false %}
                      {{ product_option_value | escape }} - {{ 'content.unavailable' | t }}
                    {% else %}
                      {{ product_option_value | escape }}
                    {% endif %}
                  </option>
                {%- endfor -%}
              </select>
              <svg
                aria-hidden="true"
                focusable="false"
                class="icon icon-caret"
                viewBox="0 0 10 6"
              >
                {%- render 'icon', icon: 'caret' -%}
              </svg>
            </div>
          </div>
        {%- endif -%}
      {%- endfor -%}

      <script type="application/json">
        {{ product_resource.selected_or_first_available_variant | json }}
      </script>
    </form>
  </variant-picker>
{% endunless %}

{% stylesheet %}
  .variant-picker {
    width: 100%;
  }

  .variant-picker__form {
    display: flex;
    flex-direction: column;
    gap: var(--padding-lg);
    width: 100%;
  }

  .variant-picker[data-shopify-visual-preview] {
    min-width: 300px;
    padding-inline-start: max(4px, var(--padding-inline-start));
  }

  .variant-option {
    --options-border-radius: var(--variant-picker-button-radius);
    --options-border-width: var(--variant-picker-button-border-width);
    --variant-option-padding-inline: var(--padding-md);
  }

  .variant-option--swatches {
    --options-border-radius: var(--variant-picker-swatch-radius);

    width: 100%;
  }

  .variant-option--swatches-disabled {
    pointer-events: none;
    cursor: not-allowed;
  }

  .variant-option--swatches > overflow-list {
    justify-content: var(--product-swatches-alignment);

    @media (width < 750px) {
      justify-content: var(--product-swatches-alignment-mobile);
    }
  }

  .variant-option--buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--gap-sm);
    margin: 0;
    padding: 0;
    border: none;
  }

  .variant-option--buttons legend {
    padding: 0;
    margin-block-end: var(--margin-xs);
  }

  .variant-option__swatch-value {
    padding-inline-start: var(--padding-xs);
    color: rgba(from var(--color-foreground) r g b / 70%);
  }

  .variant-option__button-label {
    --variant-picker-stroke-color: var(--color-variant-border);

    display: flex;
    flex: 0 0 calc(3ch + 1.3em);
    align-items: center;
    position: relative;
    padding-block: var(--padding-sm);
    padding-inline: var(--padding-lg);
    border: var(--style-border-width) solid var(--color-variant-border);
    border-radius: var(--options-border-radius);
    border-width: var(--options-border-width);
    overflow: clip;
    justify-content: center;
    min-height: calc(3ch + 1.3em);
    min-width: fit-content;
    white-space: nowrap;
    background-color: var(--color-variant-background);
    color: var(--color-variant-text);
    transition: background-color var(--animation-speed) var(--animation-easing),
      border-color var(--animation-speed) var(--animation-easing);

    &:hover {
      background-color: var(--color-variant-hover-background);
      border-color: var(--color-variant-hover-border);
      color: var(--color-variant-hover-text);
    }

    @media screen and (width >= 750px) {
      padding: var(--padding-xs) var(--variant-option-padding-inline);
    }
  }

  .variant-option__button-label__text {
    text-align: left;
    text-wrap: auto;
  }

  .variant-option--equal-width-buttons {
    --variant-min-width: clamp(44px, calc(var(--variant-option-padding-inline) * 2 + var(--variant-ch)), 100%);

    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(var(--variant-min-width), 1fr));

    .variant-option__button-label {
      min-width: var(--variant-min-width);
    }

    .variant-option__button-label__text {
      text-align: center;
      text-wrap: balance;
    }
  }

  .variant-option__button-label:has(:focus-visible) {
    --variant-picker-stroke-color: var(--color-foreground);

    border-color: var(--color-foreground);
    outline: var(--focus-outline-width) solid var(--color-foreground);
    outline-offset: var(--focus-outline-offset);
  }

  .variant-option__button-label--has-swatch {
    padding: 0;
    border: none;
    display: block;
    flex-basis: auto;
    min-height: auto;
  }

  .variant-option__button-label:has(:checked) {
    color: var(--color-selected-variant-text);
    background-color: var(--color-selected-variant-background);
    border-color: var(--color-selected-variant-border);
    transition: background-color var(--animation-speed) var(--animation-easing),
      border-color var(--animation-speed) var(--animation-easing);

    &:hover {
      background-color: var(--color-selected-variant-hover-background);
      border-color: var(--color-selected-variant-hover-border);
      color: var(--color-selected-variant-hover-text);
    }
  }

  .variant-option__button-label:has([data-option-available='false']) {
    color: rgba(from var(--color-variant-text) r g b / 60%);
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label--has-swatch:hover .swatch {
    outline: var(--focus-outline-width) solid rgba(from var(--color-foreground) r g b / 30%);
    outline-offset: var(--focus-outline-offset);
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label:has(:focus-visible) .swatch {
    outline: var(--focus-outline-width) solid currentcolor;
    outline-offset: var(--focus-outline-offset);
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label:has(:focus-visible) {
    outline: none;
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label--has-swatch:hover {
    outline: none;
  }

  .variant-option__button-label--has-swatch:hover {
    outline: var(--focus-outline-width) solid rgba(from var(--color-foreground) r g b / 30%);
    outline-offset: var(--focus-outline-offset);
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label--has-swatch:has(:checked) {
    outline: none;
  }

  .facets__inputs-list--swatches-grid .variant-option__button-label--has-swatch:has(:checked) .swatch {
    outline: var(--focus-outline-width) solid var(--color-foreground);
    outline-offset: var(--focus-outline-offset);
  }

  .variant-option__button-label--has-swatch:has(:checked) {
    outline: var(--focus-outline-width) solid var(--color-foreground);
    outline-offset: var(--focus-outline-offset);
  }

  .variant-option__button-label:has([data-option-available='false']):has(:checked) {
    --variant-picker-stroke-color: rgba(from var(--color-variant-text) r g b / 60%);

    background-color: inherit;
    color: rgba(from var(--color-variant-text) r g b / 60%);
    border-color: var(--color-selected-variant-border);
  }

  .variant-option__button-label input,
  .variant-option--images input {
    /* remove the checkbox from the page flow */
    position: absolute;

    /* set the dimensions to match those of the label */
    inset: 0;

    /* hide it */
    opacity: 0;
    margin: 0;
    cursor: pointer;
    width: 100%;
    height: 100%;
  }

  .variant-option__button-label svg {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    pointer-events: none;
    stroke-width: var(--style-border-width);
    stroke: var(--variant-picker-stroke-color);
  }

  .variant-option__select-wrapper {
    display: flex;
    position: relative;
    border: var(--style-border-width-inputs) solid var(--color-border);
    border-radius: var(--style-border-radius-inputs);
    align-items: center;
    margin-top: var(--margin-2xs);
    overflow: clip;
    transition: background-color var(--animation-speed) var(--animation-easing),
      border-color var(--animation-speed) var(--animation-easing);
  }

  .variant-option__select-wrapper:has(.swatch) {
    --variant-picker-swatch-width: 20px;
    --variant-picker-swatch-height: 20px;
  }

  .variant-option__select-wrapper:hover {
    border-color: var(--color-variant-hover-border);
  }

  .variant-option__select:focus-visible {
    outline: var(--focus-outline-width) solid currentcolor;
    outline-offset: var(--focus-outline-offset);
  }

  .variant-option__select {
    padding-block: var(--padding-md);
    padding-inline: var(--padding-lg) calc(var(--padding-lg) + var(--icon-size-2xs));
    appearance: none;
    border: 0;
    width: 100%;
    margin: 0;
    cursor: pointer;
  }

  .variant-option__select-wrapper .icon {
    position: absolute;
    right: var(--padding-md);
    top: 50%;
    transform: translateY(-50%);
    width: var(--icon-size-2xs);
    height: var(--icon-size-2xs);
    pointer-events: none;
  }

  .variant-option__select--has-swatch {
    padding-inline-start: calc((2 * var(--padding-sm)) + var(--variant-picker-swatch-width));
  }

  .variant-option__select-wrapper .swatch {
    position: absolute;
    top: 50%;
    left: var(--padding-md);
    transform: translateY(-50%);
  }

  .variant-picker--center,
  .variant-picker--center .variant-option {
    text-align: center;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .variant-picker--right,
  .variant-picker--right .variant-option {
    text-align: right;
    justify-content: right;
  }
{% endstylesheet %}

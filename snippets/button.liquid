{%- doc -%}
  Intended for use in a block similar to the button block.

  @param {string} link - link to render
  @param {object} [block] - The block

  @example
  {% render 'button', link: '/collections/all' %}
{%- enddoc -%}

<a
  {% if link == blank %}
    role="link"
    aria-disabled="true"
  {% else %}
    href="{{ link }}"
  {% endif %}
  class="
    size-style
    {{ block.settings.style_class }}
    {{ block.settings.style_class }}--{{ block.id }}
    {{ custom_css_class }}
  "
  style="{% render 'size-style', settings: block.settings %}"
  {%- if block.settings.open_in_new_tab -%}
    target="_blank"
    rel="noopener noreferrer"
  {%- endif -%}
  {{ block.shopify_attributes }}
><span class="arrow-icon"><svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>

</span>   {{ block.settings.label }}
</a>

{% stylesheet %}
  .link {
    text-decoration: none;
    text-decoration-color: currentcolor;

    &:hover {
      color: var(--color-primary-hover);
      text-decoration-color: transparent;
    }
  }
{% endstylesheet %}

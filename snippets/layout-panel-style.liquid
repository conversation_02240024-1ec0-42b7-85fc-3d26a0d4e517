{%- liquid
  comment
    Intended for blocks and sections that provide values for all the referenced settings.

    Accepts:
      settings: {block.settings || section.settings}
  endcomment

  assign horizontal_alignment = settings.horizontal_alignment

  assign vertical_alignment = settings.vertical_alignment
  if settings.align_baseline and vertical_alignment == 'flex-end'
    assign vertical_alignment = 'last baseline'
  endif

  unless settings.content_direction == 'row'
    assign horizontal_alignment = settings.horizontal_alignment_flex_direction_column
    assign vertical_alignment = settings.vertical_alignment_flex_direction_column
  endunless

  assign vertical_alignment_mobile = vertical_alignment

  if settings.vertical_on_mobile and vertical_alignment == 'last baseline'
    assign vertical_alignment_mobile = 'flex-end'
  endif
-%}

--flex-direction: {{ settings.content_direction | default: 'column' }}; --flex-wrap: nowrap;

{% render 'gap-style', value: settings.gap %}

--horizontal-alignment: {{ horizontal_alignment }}; --vertical-alignment: {{ vertical_alignment }};
--vertical-alignment-mobile: {{ vertical_alignment_mobile }};
